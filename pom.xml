<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <groupId>com.neo</groupId>
    <artifactId>neo-nova</artifactId>
    <version>1.0-SNAPSHOT</version>
    <packaging>pom</packaging>
    <name>neo_nova</name>
    <parent>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-parent</artifactId>
        <version>3.4.7</version> <!-- lookup parent from repository -->
    </parent>

    <modules>
        <module>web</module>
        <module>nova</module>
        <module>xxljob</module>
        <module>timeout</module>
        <module>neo-sdk</module>
        <module>user</module>
        <module>tagcenter</module>
        <module>auth</module>
        <module>neo-upload</module>
    </modules>

    <properties>
        <java.version>17</java.version>
        <maven.compiler.source>17</maven.compiler.source>
        <maven.compiler.target>17</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <local.repo.host>http://maven.mogu-inc.com</local.repo.host>
        <neo.client.version>1.0.4-SNAPSHOT</neo.client.version>
        <neo.common.version>1.0.4-SNAPSHOT</neo.common.version>
        <neo.cache.version>1.0-SNAPSHOT</neo.cache.version>
        <neo.default.version>1.0-SNAPSHOT</neo.default.version>
        <neo.upload.version>1.1-SNAPSHOT</neo.upload.version>
        <neo.session.sdk.version>1.0.4-SNAPSHOT</neo.session.sdk.version>
        <neo.user.version>1.0.0-SNAPSHOT</neo.user.version>
        <spring-boot.version>3.4.7</spring-boot.version>
        <mybatis-starter.version>3.0.2</mybatis-starter.version>
        <mysql-connector.version>8.0.33</mysql-connector.version>
        <mybatis-plus.version>3.5.7</mybatis-plus.version>
        <mybatis.version>3.5.16</mybatis.version>
        <mybatis-spring.version>3.0.3</mybatis-spring.version>
        <mysql-druid.version>1.2.18</mysql-druid.version>
        <lombok.version>1.18.30</lombok.version>
        <auth.client.version>1.0.1-SNAPSHOT</auth.client.version>
        <httpclient.version>4.5</httpclient.version>
        <bcprov.jdk15on.version>1.68</bcprov.jdk15on.version>
        <fastjson.version>1.2.60</fastjson.version>
        <okhttp.version>4.11.0</okhttp.version>
    </properties>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>com.neo</groupId>
                <artifactId>neo-common</artifactId>
                <version>${neo.common.version}</version>
            </dependency>

            <dependency>
                <groupId>com.neo</groupId>
                <artifactId>neo-client</artifactId>
                <version>${neo.client.version}</version>
            </dependency>

            <dependency>
                <groupId>com.neo</groupId>
                <artifactId>neo-cache</artifactId>
                <version>${neo.cache.version}</version>
            </dependency>

            <dependency>
                <groupId>com.neo</groupId>
                <artifactId>nova-adapter</artifactId>
                <version>${neo.default.version}</version>
            </dependency>
            <dependency>
                <groupId>com.neo</groupId>
                <artifactId>nova-app</artifactId>
                <version>${neo.default.version}</version>
            </dependency>
            <dependency>
                <groupId>com.neo</groupId>
                <artifactId>nova-client</artifactId>
                <version>${neo.default.version}</version>
            </dependency>
            <dependency>
                <groupId>com.neo</groupId>
                <artifactId>nova-domain</artifactId>
                <version>${neo.default.version}</version>
            </dependency>
            <dependency>
                <groupId>com.neo</groupId>
                <artifactId>nova-infrastructure</artifactId>
                <version>${neo.default.version}</version>
            </dependency>
            <dependency>
                <groupId>com.neo</groupId>
                <artifactId>timeout-adapter</artifactId>
                <version>${neo.default.version}</version>
            </dependency>
            <dependency>
                <groupId>com.neo</groupId>
                <artifactId>timeout-infrastructure</artifactId>
                <version>${neo.default.version}</version>
            </dependency>
            <dependency>
                <groupId>com.neo</groupId>
                <artifactId>timeout-app</artifactId>
                <version>${neo.default.version}</version>
            </dependency>
            <dependency>
                <groupId>com.neo</groupId>
                <artifactId>timeout-client</artifactId>
                <version>${neo.default.version}</version>
            </dependency>
            <dependency>
                <groupId>com.neo</groupId>
                <artifactId>timeout-domain</artifactId>
                <version>${neo.default.version}</version>
            </dependency>
            <dependency>
                <groupId>com.neo</groupId>
                <artifactId>upload-adapter</artifactId>
                <version>${neo.upload.version}</version>
            </dependency>

            <dependency>
                <groupId>com.neo</groupId>
                <artifactId>upload-client</artifactId>
                <version>${neo.upload.version}</version>
            </dependency>
            <dependency>
                <groupId>com.neo</groupId>
                <artifactId>upload-domain</artifactId>
                <version>${neo.upload.version}</version>
            </dependency>
            <dependency>
                <groupId>com.neo</groupId>
                <artifactId>upload-infrastructure</artifactId>
                <version>${neo.upload.version}</version>
            </dependency>
            <dependency>
                <groupId>com.neo</groupId>
                <artifactId>xxljob-adapter</artifactId>
                <version>${neo.default.version}</version>
            </dependency>
            <dependency>
                <groupId>com.neo</groupId>
                <artifactId>xxljob-app</artifactId>
                <version>${neo.default.version}</version>
            </dependency>
            <dependency>
                <groupId>com.neo</groupId>
                <artifactId>xxljob-client</artifactId>
                <version>${neo.default.version}</version>
            </dependency>
            <dependency>
                <groupId>com.neo</groupId>
                <artifactId>xxljob-domain</artifactId>
                <version>${neo.default.version}</version>
            </dependency>
            <dependency>
                <groupId>com.neo</groupId>
                <artifactId>xxljob-infrastructure</artifactId>
                <version>${neo.default.version}</version>
            </dependency>

            <dependency>
                <groupId>com.neo</groupId>
                <artifactId>tagcenter-adapter</artifactId>
                <version>${neo.default.version}</version>
            </dependency>

            <dependency>
                <groupId>com.neo</groupId>
                <artifactId>tagcenter-app</artifactId>
                <version>${neo.default.version}</version>
            </dependency>
            <dependency>
                <groupId>com.neo</groupId>
                <artifactId>tagcenter-client</artifactId>
                <version>${neo.default.version}</version>
            </dependency>
            <dependency>
                <groupId>com.neo</groupId>
                <artifactId>tagcenter-domain</artifactId>
                <version>${neo.default.version}</version>
            </dependency>
            <dependency>
                <groupId>com.neo</groupId>
                <artifactId>tagcenter-infrastructure</artifactId>
                <version>${neo.default.version}</version>
            </dependency>

            <dependency>
                <groupId>com.neo</groupId>
                <artifactId>user-adapter</artifactId>
                <version>${neo.user.version}</version>
            </dependency>
            <dependency>
                <groupId>com.neo</groupId>
                <artifactId>user-app</artifactId>
                <version>${neo.user.version}</version>
            </dependency>
            <dependency>
                <groupId>com.neo</groupId>
                <artifactId>user-client</artifactId>
                <version>${neo.user.version}</version>
            </dependency>
            <dependency>
                <groupId>com.neo</groupId>
                <artifactId>user-domain</artifactId>
                <version>${neo.user.version}</version>
            </dependency>
            <dependency>
                <groupId>com.neo</groupId>
                <artifactId>user-infrastructure</artifactId>
                <version>${neo.user.version}</version>
            </dependency>

            <dependency>
                <groupId>com.neo</groupId>
                <artifactId>auth-adapter</artifactId>
                <version>${neo.default.version}</version>
            </dependency>
            <dependency>
                <groupId>com.neo</groupId>
                <artifactId>auth-app</artifactId>
                <version>${neo.default.version}</version>
            </dependency>
            <dependency>
                <groupId>com.neo</groupId>
                <artifactId>auth-client</artifactId>
                <version>${neo.default.version}</version>
            </dependency>
            <dependency>
                <groupId>com.neo</groupId>
                <artifactId>auth-domain</artifactId>
                <version>${neo.default.version}</version>
            </dependency>
            <dependency>
                <groupId>com.neo</groupId>
                <artifactId>auth-infrastructure</artifactId>
                <version>${neo.default.version}</version>
            </dependency>

            <dependency>
                <groupId>com.neo</groupId>
                <artifactId>neo-session-sdk</artifactId>
                <version>${neo.session.sdk.version}</version>
            </dependency>

            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-web</artifactId>
                <version>${spring-boot.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter</artifactId>
                <version>${spring-boot.version}</version>
            </dependency>
            <dependency>
                <groupId>org.mybatis.spring.boot</groupId>
                <artifactId>mybatis-spring-boot-starter</artifactId>
                <version>${mybatis-starter.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-test</artifactId>
                <version>${spring-boot.version}</version>
                <scope>test</scope>
            </dependency>
            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus-boot-starter</artifactId>
                <version>${mybatis-plus.version}</version>
            </dependency>
            <dependency>
                <groupId>org.projectlombok</groupId>
                <artifactId>lombok</artifactId>
                <version>${lombok.version}</version>
            </dependency>
            <dependency>
                <groupId>mysql</groupId>
                <artifactId>mysql-connector-java</artifactId>
                <version>${mysql-connector.version}</version>
            </dependency>
            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus-extension</artifactId>
                <version>${mybatis-plus.version}</version>
                <scope>compile</scope>
            </dependency>
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>druid</artifactId>
                <version>${mysql-druid.version}</version>
            </dependency>
            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus-spring-boot3-starter</artifactId>
                <version>${mybatis-plus.version}</version>
            </dependency>
            <dependency>
                <groupId>org.mybatis</groupId>
                <artifactId>mybatis-spring</artifactId>
                <version>${mybatis-spring.version}</version>
            </dependency>

            <!-- 显式指定MyBatis版本，覆盖Spring Boot默认版本 -->
            <dependency>
                <groupId>org.mybatis</groupId>
                <artifactId>mybatis</artifactId>
                <version>${mybatis.version}</version>
            </dependency>

            <dependency>
                <groupId>org.apache.httpcomponents</groupId>
                <artifactId>httpclient</artifactId>
                <version>${httpclient.version}</version>
            </dependency>

            <dependency>
                <groupId>org.bouncycastle</groupId>
                <artifactId>bcprov-jdk15on</artifactId>
                <version>${bcprov.jdk15on.version}</version>
            </dependency>

            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>fastjson</artifactId>
                <version>${fastjson.version}</version>
            </dependency>

            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>easyexcel</artifactId>
                <version>4.0.3</version>
            </dependency>

            <dependency>
                <groupId>org.roaringbitmap</groupId>
                <artifactId>RoaringBitmap</artifactId>
                <version>1.0.1</version>
            </dependency>

            <dependency>
                <groupId>com.squareup.okhttp3</groupId>
                <artifactId>okhttp</artifactId>
                <version>${okhttp.version}</version>
            </dependency>

            <dependency>
                <groupId>org.springframework.ai</groupId>
                <artifactId>spring-ai-bom</artifactId>
                <version>1.0.0</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>dynamic-datasource-spring-boot3-starter</artifactId>
                <version>4.3.1</version>
            </dependency>


        </dependencies>
    </dependencyManagement>



    <!-- 仓库相关配置 -->
    <distributionManagement>
        <repository>
            <id>releases</id>
            <name>Nexus Release Repository</name>
            <url>${local.repo.host}/nexus/content/repositories/releases/</url>
            <uniqueVersion>false</uniqueVersion>
        </repository>
        <snapshotRepository>
            <id>snapshots</id>
            <name>Nexus Snapshot Repository</name>
            <url>${local.repo.host}/nexus/content/repositories/snapshots/</url>
            <uniqueVersion>false</uniqueVersion>
        </snapshotRepository>
    </distributionManagement>

    <build>
        <pluginManagement>
            <plugins>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-compiler-plugin</artifactId>
                    <version>3.8.1</version> <!-- 兼容 Maven 3.3+ -->
                    <configuration>
                        <source>17</source> <!-- 按需调整 JDK 版本 -->
                        <target>17</target>
                        <encoding>UTF-8</encoding> <!-- 避免编码错误 -->
                    </configuration>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-surefire-plugin</artifactId>
                    <version>2.22.2</version> <!-- 兼容 Maven 3.3+ -->
                    <configuration>
                        <skipTests>true</skipTests> <!-- 按需是否跳过测试 -->
                    </configuration>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-jar-plugin</artifactId>
                    <version>2.4</version> <!-- 兼容 Maven 3.0+ -->
                </plugin>
            </plugins>
        </pluginManagement>
    </build>


</project>
