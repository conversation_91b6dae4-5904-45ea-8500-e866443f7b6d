<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.neo</groupId>
        <artifactId>xxljob</artifactId>
        <version>1.0-SNAPSHOT</version>
    </parent>
    <artifactId>xxljob-adapter</artifactId>
    <name>xxljob-adapter</name>

    <!-- notify 不允许单独指定版本-->
    <dependencies>
        <!-- notify 此处为通用依赖引入-->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>

        <!-- notify 此处为环境引入-->

        <!-- starter-web：spring-webmvc + autoconfigure + logback + yaml + tomcat -->
        <!-- starter-test：junit + spring-test + mockito -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>

        <!-- freemarker-starter -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-freemarker</artifactId>
        </dependency>

        <!-- starter-actuator -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-actuator</artifactId>
        </dependency>

        <!-- mybatis-starter：mybatis + mybatis-spring + hikari（default） -->
        <dependency>
            <groupId>org.mybatis.spring.boot</groupId>
            <artifactId>mybatis-spring-boot-starter</artifactId>
        </dependency>
        <!-- mysql -->
        <dependency>
            <groupId>com.mysql</groupId>
            <artifactId>mysql-connector-j</artifactId>
        </dependency>


        <!-- notify 仅允许引app和client-->
        <dependency>
            <groupId>com.neo</groupId>
            <artifactId>xxljob-app</artifactId>
        </dependency>
        <dependency>
            <groupId>com.neo</groupId>
            <artifactId>xxljob-client</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>fastjson</artifactId>
            <version>2.0.46</version>
            <scope>compile</scope>
        </dependency>

        <dependency>
            <groupId>com.neo</groupId>
            <artifactId>neo-session-sdk</artifactId>
        </dependency>

    </dependencies>
</project>
