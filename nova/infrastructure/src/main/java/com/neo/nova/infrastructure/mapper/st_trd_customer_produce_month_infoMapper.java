package com.neo.nova.infrastructure.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.neo.nova.domain.entity.st_trd_customer_produce_month_info;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @description 针对表【st_trd_customer_produce_month_info(销售额月统计表)】的数据库操作Mapper
 * @createDate 2025-07-08 17:24:03
 * @Entity com.neo.nova.domain.entity.st_trd_customer_produce_month_info
 */
@Mapper
@DS("mysql")
public interface st_trd_customer_produce_month_infoMapper extends BaseMapper<st_trd_customer_produce_month_info> {


    @Select("SELECT a.customerId as customerId,\n" +
            "a.visit_date as visit_date,\n" +
            "b.matched \n" +
            "from\n" +
            "(\n" +
            "select customerId,visit_date\n" +
            "from st_trd_customer_produce_month_info\n" +
            "where produceId <=0 and fromType =0 \n" +
            "group by customerId,visit_date\n" +
            ")a\n" +
            "left join \n" +
            "(\n" +
            "select customerId,visit_date,\"1\" as matched\n" +
            "from st_trd_customer_produce_month_info\n" +
            "where produceId > 0\n" +
            "group by customerId,visit_date\n" +
            ")b \n" +
            "on a.customerId = b.customerId and a.visit_date = b.visit_date\n" +
            "WHERE b.matched is NULL")
    List<Map<String, Object>> selectUnreachableAmount();
}




