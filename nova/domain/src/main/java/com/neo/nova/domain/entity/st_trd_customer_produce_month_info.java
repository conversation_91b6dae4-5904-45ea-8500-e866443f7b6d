package com.neo.nova.domain.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 销售额月统计表
 * @TableName st_trd_customer_produce_month_info
 */
@TableName(value ="st_trd_customer_produce_month_info")
@Data
public class st_trd_customer_produce_month_info {
    /**
     * 表ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 租户ID
     */
    @TableField(value = "tenantId")
    private Long tenantId;

    /**
     * 产品ID 0代表全部
     */
    @TableField(value = "produceId")
    private Long produceId;

    /**
     * 产品名称
     */
    @TableField(value = "produceName")
    private String produceName;

    /**
     * 产品编号
     *
     */
    @TableField(value = "produceCode")
    private String produceCode;

    /**
     * 产品类型ID
     */
    @TableField(value = "produceTypeId")
    private Long produceTypeId;

    /**
     * 是否外采 0否1是
     */
    @TableField(value = "produceOemFlag")
    private Integer produceOemFlag;

    /**
     * 客户ID
     */
    @TableField(value = "customerId")
    private Long customerId;

    /**
     * 客户名称
     */
    @TableField(value = "customerName")
    private String customerName;

    /**
     * 客户名称简写
     */
    @TableField(value = "customerMnemoCode")
    private String customerMnemoCode;

    /**
     * 客户编号
     */
    @TableField(value = "customerCode")
    private String customerCode;

    /**
     * 客户类型id
     */
    @TableField(value = "customerTypeId")
    private Long customerTypeId;

    /**
     * 客户等级
     */
    @TableField(value = "customerLevel")
    private Integer customerLevel;

    /**
     * 客户渠道
     */
    @TableField(value = "channelId")
    private String channelId;

    /**
     * 大区ID
     */
    @TableField(value = "supermarketAreaId")
    private String supermarketAreaId;

    /**
     * 市场ID
     */
    @TableField(value = "marketId")
    private String marketId;

    /**
     * 销售区域ID
     */
    @TableField(value = "salesRegionId")
    private Long salesRegionId;

    /**
     * 行政区域ID
     */
    @TableField(value = "adminRegionId")
    private Long adminRegionId;

    /**
     * 业务员ID
     */
    @TableField(value = "salesId")
    private Long salesId;

    /**
     * 业务员编号
     */
    @TableField(value = "salesCode")
    private String salesCode;

    /**
     * 业务员姓名
     */
    @TableField(value = "salesName")
    private String salesName;

    /**
     * 部门id
     */
    @TableField(value = "departId")
    private Long departId;

    /**
     * 订货数量
     */
    @TableField(value = "billQty")
    private BigDecimal billQty;

    /**
     * 订货金额 元
     */
    @TableField(value = "billAmount")
    private BigDecimal billAmount;

    /**
     * 发货数量
     */
    @TableField(value = "deliveryQty")
    private BigDecimal deliveryQty;

    /**
     * 发货金额 元
     */
    @TableField(value = "deliveryAmount")
    private BigDecimal deliveryAmount;

    /**
     * 成本金额 元
     */
    @TableField(value = "costAmount")
    private BigDecimal costAmount;


    /**
     * 备用金额 元
     */
    @TableField(value = "backAmount")
    private BigDecimal backAmount;

    /**
     * 销售数量
     */
    @TableField(value = "salesQty")
    private BigDecimal salesQty;

    /**
     * 销售金额 元
     */
    @TableField(value = "salesAmount")
    private BigDecimal salesAmount;

    /**
     * 销售损耗数量
     */
    @TableField(value = "salesLossQty")
    private BigDecimal salesLossQty;

    /**
     * 销售损耗金额 元
     */
    @TableField(value = "salesLossAmount")
    private BigDecimal salesLossAmount;

    /**
     * 2025-01
     */
    @TableField(value = "visit_date")
    private String visitDate;

    /**
     * key: inputSummary value: 1
     */
    @TableField(value = "extra")
    private String extra;


    /**
     * 创建时间
     */
    @TableField(value = "created")
    private Long created;

    /**
     * 更新时间
     */
    @TableField(value = "updated")
    private Long updated;

    /**
     * 0正常1已删除
     */
    @TableField(value = "isDeleted")
    private Integer isDeleted;
}
