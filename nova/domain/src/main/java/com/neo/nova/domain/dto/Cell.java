package com.neo.nova.domain.dto;

import lombok.Builder;
import lombok.Data;

import java.util.HashMap;
import java.util.Map;

/**
 * Created by juanmao
 */
@Data
@Builder
public class Cell {

    private Object value;

    private Map<String, Object> info;

    private Map<String, Object> extraData;

    //F43E3E
    public Cell setColor(String color) {
        if (info == null) info = new HashMap<>();
        info.put("color", color);
        return this;
    }

    //加粗
    public Cell setBold() {
        if (info == null) info = new HashMap<>();
        info.put("bold", true);
        return this;
    }

    public Cell setPercent() {
        if (info == null) info = new HashMap<>();
        info.put("percent", true);
        return this;
    }

    public Cell setToLocaleString() {
        if (info == null) info = new HashMap<>();
        info.put("toLocaleString", true);
        return this;
    }


    public Cell extraData(String key, Object value) {
        if (extraData == null) extraData = new HashMap<>();
        extraData.put(key, value);
        return this;
    }

}
