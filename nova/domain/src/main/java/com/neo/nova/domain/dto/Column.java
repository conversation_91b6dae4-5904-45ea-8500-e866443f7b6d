package com.neo.nova.domain.dto;

import com.neo.nova.domain.enums.TargetDataTypeEnum;
import lombok.Builder;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * Created by juanmao
 * 复杂列
 *
 * @since 2019-06-04
 */
@Data
@Builder
public class Column {
    /**
     * 列类型
     */
    private Integer columnType = 0;
    /**
     * 唯一标识
     */
    private String key;
    /**
     * 标题
     */
    private String title;
    /**
     * 是否支持排序
     */
    private boolean isSort = false;

    /**
     * 表头是否固定
     */
    private boolean isFixed = false;

    /**
     * 全列百分比
     */
    private boolean percent = false;

    /**
     * 全列数据科学计数法
     */
    private boolean toLocaleString = false;

    /**
     * 复杂表头
     */
    private List<Column> children;

    public Column addChild(Column column) {
        if (children == null) {
            children = new ArrayList<>();
        }
        children.add(column);
        return this;
    }

    public static Column targetColumn(String targetTypeCode) {
        ColumnBuilder builder = Column.builder().key(targetTypeCode).title(TargetDataTypeEnum.getNameByCode(targetTypeCode))
                .isSort(true);
        if (TargetDataTypeEnum.isPercent(targetTypeCode)) {
            builder.percent(true);
        } else {
            builder.toLocaleString(true);
        }
        return builder.build();
    }

}
