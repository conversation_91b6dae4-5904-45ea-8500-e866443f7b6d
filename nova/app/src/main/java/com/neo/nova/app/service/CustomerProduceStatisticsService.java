package com.neo.nova.app.service;

import com.neo.nova.domain.dto.CustomerStatisticsDetailDTO;
import com.neo.nova.domain.entity.st_trd_customer_produce_day_info;
import com.neo.nova.domain.entity.st_trd_customer_produce_month_info;

import java.util.List;

/**
 * 客户产品统计数据服务
 * 负责处理 st_trd_customer_produce_day_info 和 st_trd_customer_produce_month_info 表的新增/修改操作
 *
 * <AUTHOR>
 * @since 2025-07-24
 */
public interface CustomerProduceStatisticsService {

    /**
     * 清理无法触达的日表数据
     *
     * @param dayInfoList 订单详情
     * @param tenantId    租户ID
     */
    void clearUnReachableDayData(List<st_trd_customer_produce_day_info> dayInfoList, Long tenantId);


    /**
     * 清理无法触达的月表数据
     *
     * @param monthInfoList 订单详情
     * @param tenantId      租户ID
     */
    void clearUnReachableMonthData(List<st_trd_customer_produce_month_info> monthInfoList, Long tenantId);

    /**
     * 检查客户信息
     *
     * @param tenantId 租户ID
     */
    void customerInfoCheck(Long tenantId);


    /**
     * 加工并保存销量数据
     *
     * @param customerStatisticsDetailDTOS 客户统计详情
     * @param tenantId                     租户ID
     */
    void processAndSaveRecords(List<CustomerStatisticsDetailDTO> customerStatisticsDetailDTOS, Long tenantId);

    /**
     * 保存日统计数据
     * 不存在就新增，存在就修改（仅在数据有变化时更新）
     *
     * @param dayInfoList 日统计数据列表
     * @param tenantId    租户ID
     */
    void saveDayInfoRecords(List<st_trd_customer_produce_day_info> dayInfoList, Long tenantId);

    /**
     * 保存月统计数据
     * 单月合计 = SUM（单日 合计）
     * 单月货品 = SUM（单日 货品）
     * 如果传入单月货品，不会自动归并到单月合计，需要额外传入单月合计。
     * 以上计算方式无优先级，因此如果单独传入月合计类数据，需确保该数据无单日货品、单日合计类数据，否则会被覆盖。
     *
     * @param monthInfoList 月统计数据列表
     * @param tenantId      租户ID
     * @param fromDay       日维度上报
     */
    void saveMonthInfoRecords(List<st_trd_customer_produce_month_info> monthInfoList, Long tenantId, boolean fromDay);

    /**
     * 上报单品更新数据到月表
     *
     * @param dayInfoList 日统计数据列表
     * @param tenantId    租户ID
     */
    void aggregateDayToMonthRecords(List<st_trd_customer_produce_day_info> dayInfoList, Long tenantId);


}
