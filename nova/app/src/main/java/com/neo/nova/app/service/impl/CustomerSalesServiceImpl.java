package com.neo.nova.app.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.LocalDateTimeUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.neo.api.MultiResponse;
import com.neo.api.PageResponse;
import com.neo.api.SingleResponse;
import com.neo.nova.app.exception.BizCustomException;
import com.neo.nova.app.helper.ChartHelper;
import com.neo.nova.app.service.CustomerSalesService;
import com.neo.nova.app.service.CustomerService;
import com.neo.nova.app.service.MetricService;
import com.neo.nova.app.service.PerformanceService;
import com.neo.nova.app.vo.*;
import com.neo.nova.domain.constants.DataConstants;
import com.neo.nova.domain.dto.*;
import com.neo.nova.domain.entity.*;
import com.neo.nova.domain.enums.*;
import com.neo.nova.domain.excelExport.StatisticsExport;
import com.neo.nova.domain.gateway.st_trd_customer_produce_day_infoRepository;
import com.neo.nova.domain.gateway.st_trd_customer_produce_month_infoRepository;
import com.neo.user.client.tenant.api.DepartmentService;
import jakarta.annotation.Resource;
import lombok.Getter;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.math.RoundingMode;

import com.neo.nova.domain.enums.TargetDataTypeEnum;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.Objects;

import static com.neo.nova.domain.constants.DataConstants.AMOUNT_VALID_SUMMARY_GOODS_ID;
import static com.neo.nova.domain.constants.DataConstants.SUMMARY_GOODS_ID;
import static com.neo.nova.domain.enums.TargetDataTypeEnum.*;


@Service
public class CustomerSalesServiceImpl implements CustomerSalesService {
    @Autowired
    private st_trd_customer_produce_day_infoRepository st_trd_customer_produce_day_infoRepository;
    @Autowired
    private st_trd_customer_produce_month_infoRepository st_trd_customer_produce_month_infoRepository;
    @Resource
    private MetricService metricService;
    @Resource
    private PerformanceService performanceService;
    @Resource
    private CustomerService customerService;
    @Resource
    private DepartmentService departmentService;

    /**
     * 销售额统计
     *
     * @param dataFormQueryVO
     * @return
     */
    @Override
    public DataFormVO statistics(DataFormQueryVO dataFormQueryVO) {
        //合计
        dataFormQueryVO.setTrend(false);

        // 表格查询 - 当前数据
        PageResponse<Map<String, Object>> tableRecordsResponse = batchActualValue(dataFormQueryVO);
        if (tableRecordsResponse == null || tableRecordsResponse.getData() == null) {
            return null;
        }
        List<Map<String, Object>> tableRecords = tableRecordsResponse.getData();

        // 组装表格
        Table table = new Table();
        // 添加查询条件列
        dataFormQueryVO.getQueryOptions().keySet().stream().sorted(MetricCodeEnum::comparator).forEach(table::addMetricCodeColumn);
        // 添加数据列
        List<String> dataColumns = TargetDataTypeEnum.listTableColumns(dataFormQueryVO.getTargetDataTypes(), dataFormQueryVO.getTrend());
        dataColumns.forEach(dataColumn -> table.addToFirstColumn(Column.targetColumn(dataColumn)));
        // 填充表格数据
        tableRecords.forEach(table::addRecord);

        // 设置分页信息
        table.setPageSize(tableRecordsResponse.getPageSize());
        table.setTotalPage(tableRecordsResponse.getTotalPages());
        table.setCurrentPage(tableRecordsResponse.getPageIndex());
        table.setTotal(tableRecordsResponse.getTotalCount());

        // 组装返回结果
        DataFormVO dataFormVO = new DataFormVO();
        dataFormVO.setTable(table);

        return dataFormVO;
    }


    /**
     * 销售趋势
     *
     * @param dataFormQueryVO 查询条件
     * @return 趋势数据
     */
    @Override
    public DataFormVO trends(DataFormQueryVO dataFormQueryVO) {

        //趋势
        dataFormQueryVO.setTrend(true);

        //表格数据
        List<Map<String, Object>> fullTableRecords = _batchTrendValue(dataFormQueryVO);
        if (CollectionUtils.isEmpty(fullTableRecords) || fullTableRecords.get(0) == null) {
            return null;
        }

        // 查询中文名称
        batchMapIdToNames(fullTableRecords, dataFormQueryVO.getTenantId());

        //趋势默认时间倒序
        if (dataFormQueryVO.getSortBy() == null) dataFormQueryVO.setSortBy("visit_date");

        PageResponse<Map<String, Object>> response = processResponse(fullTableRecords, dataFormQueryVO);
        // 组装表格
        Table table = new Table();
        table.addToFirstColumn(Column.builder().key("visit_date").title("日期").isSort(true).build());
        // 添加查询条件列
        dataFormQueryVO.getQueryOptions().keySet().stream().sorted(MetricCodeEnum::comparator).forEach(table::addMetricCodeColumn);
        // 添加数据列
        List<String> dataColumns = TargetDataTypeEnum.listTableColumns(dataFormQueryVO.getTargetDataTypes(), dataFormQueryVO.getTrend());
        dataColumns.forEach(dataColumn -> table.addToFirstColumn(Column.targetColumn(dataColumn)));
        // 填充表格数据
        response.getData().forEach(table::addRecord);

        // 设置分页信息
        table.setPageSize(response.getPageSize());
        table.setTotalPage(response.getTotalPages());
        table.setCurrentPage(response.getPageIndex());
        table.setTotal(response.getTotalCount());

        Chart<String, Cell> chart = buildChart(dataFormQueryVO, fullTableRecords);

        DataFormVO dataFormVO = new DataFormVO();
        dataFormVO.setTable(table);
        dataFormVO.setChart(chart);
        return dataFormVO;
    }

    private Chart<String, Cell> buildChart(DataFormQueryVO dataFormQueryVO, List<Map<String, Object>> tableRecords) {

        //选择的列
        List<String> selectColumns = dataFormQueryVO.getQueryOptions().keySet().stream().map(MetricCodeEnum::tableNameMapping).toList();
        //展示key
        String chartShowKey = TargetDataTypeEnum.getChartShowKey(dataFormQueryVO.getTargetDataTypes());
        //附加key
        List<String> extraKeys = TargetDataTypeEnum.listTableColumns(dataFormQueryVO.getTargetDataTypes(), true);

        // 组装chart
        Chart<String, Cell> chart = ChartHelper.buildMultiDimensionChart(tableRecords, selectColumns
                , "visit_date", chartShowKey, extraKeys);

        //放入中文描述
        Map<String, Cell> keyColumn = new HashMap<>();
        extraKeys.forEach(targetDataType -> {
            Cell cell = Cell.builder().value(TargetDataTypeEnum.getNameByCode(targetDataType)).build();
            if (TargetDataTypeEnum.isPercent(targetDataType)) {
                cell.setPercent();
            }
            keyColumn.put(targetDataType, cell);
        });
        chart.addExtra("keyColumn", keyColumn);

        return chart;

    }


    private static void timeConditionCheck(TimeCondition timeCondition) {
        if (timeCondition == null) {
            throw new BizCustomException(104, "时间条件不能为空");
        }
        //对periodType进行合法校验
        Integer periodType = timeCondition.getPeriodType();
        if (Arrays.stream(PeriodTypeEnum.values()).map(PeriodTypeEnum::getCode).noneMatch(code -> code.equals(periodType))) {
            throw new BizCustomException(100, "无效的周期类型: " + periodType);
        }
        //对时间进行校验
        String startDate = timeCondition.getStartDate();
        String endDate = timeCondition.getEndDate();

        if (timeCondition.getPeriodType().equals(PeriodTypeEnum.CUSTOM_DAY.getCode())) {
            // 校验时间格式是否为 yyyy-MM-dd
            if (startDate == null || !startDate.matches("\\d{4}-\\d{2}-\\d{2}")) {
                throw new BizCustomException(101, "开始时间格式不正确，应为 yyyy-MM-dd: " + startDate);
            }
            if (endDate == null || !endDate.matches("\\d{4}-\\d{2}-\\d{2}")) {
                throw new BizCustomException(102, "结束时间格式不正确，应为 yyyy-MM-dd: " + endDate);
            }
        } else {
            // 校验时间格式是否为 yyyy-MM
            if (startDate == null || !startDate.matches("\\d{4}-\\d{2}")) {
                throw new BizCustomException(101, "开始时间格式不正确，应为 yyyy-MM: " + startDate);
            }
            if (endDate == null || !endDate.matches("\\d{4}-\\d{2}")) {
                throw new BizCustomException(102, "结束时间格式不正确，应为 yyyy-MM: " + endDate);
            }
        }

        // 校验开始时间不能晚于结束时间
        if (startDate.compareTo(endDate) > 0) {
            throw new BizCustomException(103, "开始时间不能晚于结束时间");
        }

    }


    private static <T> void wrapperAssembly(QueryWrapper<T> wrapper, String metricCode, List<String> values) {
        String selectColumn = MetricCodeEnum.metricCodeMapping(metricCode);
        wrapper.groupBy(selectColumn);
        valueSearchSet(wrapper, metricCode, selectColumn, values);
        //查询复杂指标时，去除空项
        if (MetricCodeEnum.isComplexMetric(metricCode)) {
            wrapper.ne(selectColumn, "").isNotNull(selectColumn);
        }
    }

    private static <T> void wrapperValueSearch(QueryWrapper<T> wrapper, String metricCode, List<String> values) {
        String selectColumn = MetricCodeEnum.metricCodeMapping(metricCode);
        if (selectColumn == null) {
            return; // 如果映射不存在，跳过该条件
        }

        valueSearchSet(wrapper, metricCode, selectColumn, values);

        //查询复杂指标时，去除空项
        if (MetricCodeEnum.isComplexMetric(metricCode)) {
            wrapper.ne(selectColumn, "").isNotNull(selectColumn);
        }
    }

    private static <T> void valueSearchSet(QueryWrapper<T> wrapper, String metricCode, String selectColumn, List<String> values) {
        if (CollectionUtil.isEmpty(values)) {
            return;
        }
        //简写搜索特判
        if (MetricCodeEnum.CUSTOMER_NAME.getCode().equals(metricCode)) {
            wrapper.nested(wq ->
                    wq.like(selectColumn, values.get(0))
                            .or()
                            .likeLeft("customerMnemoCode", values.get(0))
            );
            return;
        }
        if (MetricCodeEnum.PRODUCT_NAME.getCode().equals(metricCode)
                || MetricCodeEnum.CUSTOMER_OWNER.getCode().equals(metricCode)) {
            wrapper.like(selectColumn, values.get(0));
            return;
        }

        wrapper.in(selectColumn, values);
    }


    /**
     * 饼图统计
     *
     * @param currentTime 格式：YYYYMM，例如：202501
     * @return 饼图数据
     */
    @Override
    public PieChartVO pieChart(Long tenantId, String currentTime) {
        try {

            LocalDateTime localDateTime = LocalDateTimeUtil.parse(currentTime, "yyyy-MM");

            String yearStartMonth = localDateTime.getYear() + "-01";
            String yearEndMonth = localDateTime.getYear() + "-12";

            // 获取目标值
            BigDecimal yearTargetValue = performanceService.queryRootTargets(tenantId, new TimeCondition(PeriodTypeEnum.YEAR.getCode(), yearStartMonth, yearEndMonth));
            BigDecimal monthTargetValue = performanceService.queryRootTargets(tenantId, new TimeCondition(PeriodTypeEnum.MONTH.getCode(), currentTime, currentTime));

            // 获取实际销售额

            BigDecimal yearActualValue = _queryAllCompany(tenantId, new TimeCondition(PeriodTypeEnum.YEAR.getCode(), yearStartMonth, yearEndMonth));
            BigDecimal monthActualValue = _queryAllCompany(tenantId, new TimeCondition(PeriodTypeEnum.MONTH.getCode(), currentTime, currentTime));

            // 计算时间进度
            TimeProgress timeProgress = calculateTimeProgress(localDateTime);

            // 构建返回结果
            PieChartVO pieChartVO = PieChartVO.builder()
                    .yearTargetValue(yearTargetValue)
                    .monthTargetValue(monthTargetValue)
                    .yearActualValue(yearActualValue)
                    .monthActualValue(monthActualValue)
                    .yearTimePercent(timeProgress.getYearTimePercent())
                    .monthTimePercent(timeProgress.getMonthTimePercent())
                    .build();

            // 计算实现率、状态和剩余目标
            pieChartVO.calculatePercents();
            pieChartVO.calculateStatus();
            pieChartVO.calculateRemainingTargets();

            return pieChartVO;

        } catch (BizCustomException e) {
            throw e;
        } catch (Exception e) {
            throw new BizCustomException(500, "饼图数据查询失败：" + e.getMessage());
        }
    }

    @Override
    public Map<Long, PerformanceProgressDTO> getProgress(Long tenantId, List<Long> userIds, TimeCondition timeCondition) {
        if (timeCondition == null || timeCondition.getStartDate() == null) {
            return null;
        }

        Map<Long, PerformanceProgressDTO> result = Maps.newHashMap();
        try {
            // 2. 查询目标值
            Map<Long, BigDecimal> targetValues = performanceService.queryUserTargets(tenantId, userIds, timeCondition);

            // 3. 批量查询实际进度值
            Map<Long, BigDecimal> progressValueMap = queryActualValueForUser(tenantId, userIds, timeCondition);

            // 4. 计算完成百分比
            for (Long userId : userIds) {
                BigDecimal targetValue = targetValues.getOrDefault(userId, BigDecimal.ZERO);
                BigDecimal progressValue = progressValueMap.getOrDefault(userId, BigDecimal.ZERO);
                BigDecimal percent = BigDecimal.ZERO;
                if (targetValue.compareTo(BigDecimal.ZERO) > 0) {
                    percent = progressValue.divide(targetValue, 4, RoundingMode.HALF_UP)
                            .multiply(new BigDecimal("100"))
                            .setScale(2, RoundingMode.HALF_UP);
                }
                result.put(userId, PerformanceProgressDTO.builder()
                        .timeCondition(timeCondition)
                        .progressValue(progressValue)
                        .target(targetValue)
                        .percent(percent)
                        .build());
            }
        } catch (Exception e) {
            throw new BizCustomException(500, "查询进度失败：" + e.getMessage());
        }
        return result;
    }

    /**
     * 计算时间进度
     */
    private TimeProgress calculateTimeProgress(LocalDateTime localDateTime) {
        LocalDateTime now = LocalDateTimeUtil.now();

        // 未来
        if (now.getYear() > localDateTime.getYear() || now.getMonthValue() > localDateTime.getMonthValue()) {
            // 未来时间，进度为0
            return new TimeProgress(BigDecimal.ZERO, BigDecimal.ZERO);
        }
        //过去
        if (now.getYear() < localDateTime.getYear() || now.getMonthValue() < localDateTime.getMonthValue()) {
            return new TimeProgress(BigDecimal.ONE, BigDecimal.ONE);
        }

        // 年度时间进度
        BigDecimal yearTimePercent = new BigDecimal(now.getMonthValue()).divide(new BigDecimal("12"), 4, RoundingMode.HALF_UP);
        // 当前月份，按天计算
        BigDecimal monthTimePercent = new BigDecimal(now.getDayOfMonth()).divide(new BigDecimal(now.getMonth().length(now.getYear() % 4 == 0)), 4, RoundingMode.HALF_UP);

        return new TimeProgress(yearTimePercent, monthTimePercent);
    }

    /**
     * 时间进度内部类
     */
    @Getter
    private static class TimeProgress {
        private final BigDecimal yearTimePercent;
        private final BigDecimal monthTimePercent;

        public TimeProgress(BigDecimal yearTimePercent, BigDecimal monthTimePercent) {
            this.yearTimePercent = yearTimePercent;
            this.monthTimePercent = monthTimePercent;
        }
    }

    @Override
    public List<Map<String, Object>> StatisticsExport(DataFormQueryVO dataFormQueryVO) {
        // 验证参数
        validate(dataFormQueryVO);
        //合计
        dataFormQueryVO.setTrend(false);

        // 表格查询 - 当前数据 (与 statistics 方法保持一致)
        List<Map<String, Object>> tableRecords = _batchActualValue(dataFormQueryVO, 0);
        if (tableRecords == null) {
            return Collections.emptyList();
        }

        // 计算总计数据用于占比计算
        List<Map<String, Object>> actualValue = _batchActualValue(dataFormQueryVO, 1);
        Map<String, Object> totalData = Optional.ofNullable(actualValue)
                .filter(list -> !list.isEmpty())
                .map(list -> list.get(0))
                .orElse(Maps.newHashMap());

        // 计算环比数据
        TimeCondition momTimeCondition = dataFormQueryVO.getTimeCondition().copy().initMom();
        Map<String, Map<String, Object>> momData = _calculatePreData(dataFormQueryVO, momTimeCondition, 2);

        // 计算同比数据
        TimeCondition yoyTimeCondition = dataFormQueryVO.getTimeCondition().copy().initYoy();
        Map<String, Map<String, Object>> yoyData = _calculatePreData(dataFormQueryVO, yoyTimeCondition, 3);

        // 结果计算
        enhanceStaticsRecords(tableRecords, totalData, momData, yoyData, dataFormQueryVO);

        // 批量映射ID到中文名称
        batchMapIdToNames(tableRecords, dataFormQueryVO.getTenantId());

        return tableRecords;
    }


    /**
     * 安全获取String值
     */
    private String getStringValue(Map<String, Object> map, String key) {
        Object value = map.get(key);
        return value != null ? value.toString() : "";
    }


    @Override
    public List<Map<String, Object>> getDynamicExportData(DataFormQueryVO dataFormQueryVO) {
        if (dataFormQueryVO == null) {
            return Collections.emptyList();
        }
        //趋势
        dataFormQueryVO.setTrend(true);

        // 时间合法性校验
        TimeCondition timeCondition = dataFormQueryVO.getTimeCondition();
        timeConditionCheck(timeCondition);

        //表格数据 - 仿照trends方法实现
        List<Map<String, Object>> fullTableRecords = _batchTrendValue(dataFormQueryVO);
        if (CollectionUtils.isEmpty(fullTableRecords) || fullTableRecords.get(0) == null) {
            return Collections.emptyList();
        }

        //趋势默认时间倒序
        if (dataFormQueryVO.getSortBy() == null) dataFormQueryVO.setSortBy("visit_date");

        dataFormQueryVO.setPageSize(Integer.MAX_VALUE);
        dataFormQueryVO.setPageIndex(1);

        // 设置一个足够大的页大小以获取所有数据
        PageResponse<Map<String, Object>> response = processResponse(fullTableRecords, dataFormQueryVO);
        // 查询中文名称
        batchMapIdToNames(response.getData(), dataFormQueryVO.getTenantId());

        // 返回所有数据而不是分页数据
        return response.getData();
    }


    /**
     * 批量映射ID到中文名称
     */
    private void batchMapIdToNames(List<Map<String, Object>> resultMaps, Long tenantId) {
        if (resultMaps == null || resultMaps.isEmpty()) {
            return;
        }

        // 收集所有需要映射的ID
        Set<Long> customerSalesRegionMap = new HashSet<>();
        Set<Long> adminRegionIds = new HashSet<>();
        Set<Long> produceTypeIds = new HashSet<>();
        Set<Long> customerTypeIds = new HashSet<>();
        Set<Long> salesIds = new HashSet<>();
        Set<Long> departIds = new HashSet<>();
        Set<Long> customerIds = new HashSet<>();
        Set<Long> goodsIds = new HashSet<>();

        for (Map<String, Object> map : resultMaps) {
            collectId(map, "salesRegionId", customerSalesRegionMap);
            collectId(map, "adminRegionId", adminRegionIds);
            collectId(map, "produceTypeId", produceTypeIds);
            collectId(map, "customerTypeId", customerTypeIds);
            collectId(map, "salesId", salesIds);
            collectId(map, "departId", departIds);
            collectId(map, "customerId", customerIds);
            collectId(map, "productId", goodsIds);
        }

        // 批量查询名称映射
        Map<Long, String> customerSalesRegionNameMap = metricService.queryNames(tenantId, MetricCodeEnum.CUSTOMER_SALES_REGION.getCode(), customerSalesRegionMap);
        Map<Long, String> adminRegionNameMap = metricService.queryNames(tenantId, MetricCodeEnum.CUSTOMER_ADMIN_REGION.getCode(), adminRegionIds);
        Map<Long, String> produceTypeNameMap = metricService.queryNames(tenantId, MetricCodeEnum.PRODUCT_TYPE.getCode(), produceTypeIds);
        Map<Long, String> customerTypeNameMap = metricService.queryNames(tenantId, MetricCodeEnum.CUSTOMER_TYPE.getCode(), customerTypeIds);
        Map<Long, String> salesNameMap = metricService.queryNames(tenantId, MetricCodeEnum.USER.getCode(), salesIds);
        Map<Long, String> departNameMap = metricService.queryNames(tenantId, MetricCodeEnum.DEPARTMENT.getCode(), departIds);
        Map<Long, String> customerNameMap = metricService.queryNames(tenantId, MetricCodeEnum.CUSTOMER.getCode(), customerIds);
        Map<Long, String> goodsNameMap = metricService.queryNames(tenantId, MetricCodeEnum.PRODUCT.getCode(), goodsIds);

        // 将名称映射回原始数据
        for (Map<String, Object> map : resultMaps) {
            Optional.ofNullable(map.get("channelId")).map(String::valueOf).map(MetricCodeIdEnum::getByCode)
                    .ifPresent(metricCodeIdEnum -> map.put("channelName", metricCodeIdEnum.getName()));
            Optional.ofNullable(map.get("supermarketAreaId")).map(String::valueOf).map(MetricCodeIdEnum::getByCode)
                    .ifPresent(metricCodeIdEnum -> map.put("supermarketAreaName", metricCodeIdEnum.getName()));
            Optional.ofNullable(map.get("marketId")).map(String::valueOf).map(MetricCodeIdEnum::getByCode)
                    .ifPresent(metricCodeIdEnum -> map.put("marketName", metricCodeIdEnum.getName()));
            Optional.ofNullable(map.get("produceOemFlag")).map(String::valueOf).map(Integer::valueOf).map(GoodsSourceEnum::getByCode)
                    .ifPresent(goodsSourceEnum -> map.put("produceSourceName", goodsSourceEnum.getDesc()));
            Optional.ofNullable(map.get("customerLevel")).map(String::valueOf).map(Long::valueOf).map(CustomerLevelEnum::getById)
                    .ifPresent(customerLevelEnum -> map.put("customerLevelName", customerLevelEnum.getDesc()));
            mapIdToName(map, "salesRegionId", "salesRegionName", customerSalesRegionNameMap);
            mapIdToName(map, "adminRegionId", "adminRegionName", adminRegionNameMap);
            mapIdToName(map, "produceTypeId", "produceTypeName", produceTypeNameMap);
            mapIdToName(map, "customerTypeId", "customerTypeName", customerTypeNameMap);
            mapIdToName(map, "salesId", "salesName", salesNameMap);
            mapIdToName(map, "departId", "departName", departNameMap);
            mapIdToName(map, "customerId", "customerIdName", customerNameMap);
            mapIdToName(map, "produceId", "produceIdName", goodsNameMap);

        }
    }

    /**
     * 从Map中收集指定字段的ID
     */
    private void collectId(Map<String, Object> map, String fieldName, Set<Long> idSet) {
        Object value = map.get(fieldName);
        if (value != null) {
            try {
                Long id = Long.valueOf(value.toString());
                idSet.add(id);
            } catch (NumberFormatException e) {
                // 忽略无效的ID
            }
        }
    }

    /**
     * 将ID映射为名称并添加到Map中
     */
    private void mapIdToName(Map<String, Object> map, String idFieldName, String nameFieldName, Map<Long, String> nameMap) {
        Object idValue = map.get(idFieldName);
        if (idValue != null) {
            try {
                Long id = Long.valueOf(idValue.toString());
                String name = nameMap.get(id);
                if (name != null) {
                    map.put(nameFieldName, name);
                }
            } catch (NumberFormatException e) {
                // 忽略无效的ID
            }
        }
    }

    @Override
    public DataFormVO salesReport(DataFormQueryVO queryVO) {
//        List<CustomerDTO> customerInfoList = customerService.searchDTO(CustomerQueryVO.builder().tenantId(queryVO.getTenantId())
//                .status(Lists.newArrayList(CustomerStatusEnum.NORMAL.getCode()))
//                .channelId(queryVO.getChannelMetricCodeId()).build());
//        if (customerInfoList == null || customerInfoList.isEmpty()) {
//            return null;
//        }
//        List<Long> customerIds = customerInfoList.stream().map(CustomerDTO::getId).toList();
//        Map<Long, CustomerDTO> customerInfoMap = customerInfoList.stream()
//                .collect(Collectors.toMap(CustomerDTO::getId,
//                        Function.identity(),
//                        (customerInfo, customerInfo2) -> customerInfo
//                ));

        queryVO.getTimeCondition().setEndDate(queryVO.getTimeCondition().getStartDate());
        if (!queryVO.getQueryOptions().containsKey(MetricCodeEnum.CHANNEL.getCode())) {
            queryVO.getQueryOptions().put(MetricCodeEnum.CHANNEL.getCode(), null);
        }
        queryVO.getQueryOptions().put(MetricCodeEnum.AREA.getCode(), null);
        queryVO.getQueryOptions().put(MetricCodeEnum.CUSTOMER.getCode(), null);
        queryVO.getQueryOptions().put(MetricCodeEnum.CUSTOMER_OWNER.getCode(), null);
        queryVO.setTargetDataTypes(Lists.newArrayList(TargetDataTypeEnum.SALES_VALID_AMOUNT.getCode(), TargetDataTypeEnum.COST_VALID_AMOUNT.getCode(),
                TargetDataTypeEnum.GROSS_PROFIT_VALID_AMOUNT.getCode(), TargetDataTypeEnum.GROSS_PROFIT_VALID_MARGIN.getCode()));
        queryVO.setTrend(false);
        queryVO.setSortBy("channelName");
        queryVO.setPageSize(1000);

        PageResponse<Map<String, Object>> response = batchActualValue(queryVO);
        List<Map<String, Object>> records = response.getData();

        // 构建表格
        Table table = new Table();
        table.setTitle("销售报表");

        List<String> channelCodeIds = queryVO.getQueryOptions().get(MetricCodeEnum.CHANNEL.getCode());
        if (channelCodeIds == null) channelCodeIds = Lists.newArrayList();
        buildSalesReportTitle(table, channelCodeIds);

        for (Map<String, Object> record : records) {

            table.addRecord(record);
            //销售收入
            BigDecimal salesAmount = new BigDecimal(record.getOrDefault(TargetDataTypeEnum.SALES_VALID_AMOUNT.getCode(), "0").toString())
                    .setScale(2, RoundingMode.HALF_UP);

            //毛利
            BigDecimal grossProfitAmount = new BigDecimal(record.getOrDefault(TargetDataTypeEnum.GROSS_PROFIT_VALID_AMOUNT.getCode(), "0").toString())
                    .setScale(2, RoundingMode.HALF_UP);

            //毛利率
            Cell grossProfitValidMarginCell = table.getLastRow().get(TargetDataTypeEnum.GROSS_PROFIT_VALID_MARGIN.getCode());
            if (grossProfitValidMarginCell != null) {
                BigDecimal grossProfitValidMargin = new BigDecimal(grossProfitValidMarginCell.getValue().toString());
                if (channelCodeIds.contains(MetricCodeIdEnum.CHANNEL_JOINT_SUPERMARKET.getCode())) {
                    if (grossProfitValidMargin.compareTo(new BigDecimal("30")) < 0) {
                        grossProfitValidMarginCell.setColor("F43E3E").setBold();
                    }
                } else {
                    if (grossProfitValidMargin.compareTo(new BigDecimal("10")) < 0) {
                        grossProfitValidMarginCell.setColor("F43E3E").setBold();
                    }
                }
            }

            //销售扣点
            BigDecimal marketDeducePoint = BigDecimal.ZERO;
            //固定耗材费用
            BigDecimal fixedConsumablesCost = BigDecimal.ZERO;
            //导购管理费
            BigDecimal guideManagementCost = BigDecimal.ZERO;
            //电费
            BigDecimal electricityCost = BigDecimal.ZERO;
            //配送费
            BigDecimal deliveryCost = BigDecimal.ZERO;
            //促销费
            BigDecimal promotionCost = BigDecimal.ZERO;
            //推广费
            BigDecimal marketingCost = BigDecimal.ZERO;
            //进场费
            BigDecimal slottingFee = BigDecimal.ZERO;
            //其他费用
            BigDecimal otherCosts = BigDecimal.ZERO;
            //小计
            BigDecimal supermarketCostSubtotal = BigDecimal.ZERO;

            //销售扣点
            marketDeducePoint = salesAmount.multiply(BigDecimal.valueOf(0.13)).setScale(2, RoundingMode.HALF_UP);

            //小计
            supermarketCostSubtotal = marketDeducePoint.add(fixedConsumablesCost).add(guideManagementCost)
                    .add(electricityCost).add(deliveryCost).add(promotionCost).add(marketingCost)
                    .add(slottingFee).add(otherCosts);
            table.addToLastRow("marketDeducePoint", Cell.builder().value(marketDeducePoint).build());
            table.addToLastRow("supermarketCostSubtotal", Cell.builder().value(supermarketCostSubtotal).build());

            //员工社保
            BigDecimal employeeCost = BigDecimal.ZERO;

            //出清损耗
            BigDecimal clearanceLoss = BigDecimal.ZERO;
            //损耗
            BigDecimal wastageLoss = BigDecimal.ZERO;
            //小计
            BigDecimal lossSubtotal = clearanceLoss.add(wastageLoss);

            //冰柜折旧
            BigDecimal freezerCost = BigDecimal.ZERO;
            //基础物料
            BigDecimal basicSuppliesCost = BigDecimal.ZERO;
            //试吃物料
            BigDecimal productSamplingCost = BigDecimal.ZERO;
            //自购物料
            BigDecimal personalBuyCost = BigDecimal.ZERO;
            //小计
            BigDecimal supermarketSetupSubtotal = freezerCost.add(basicSuppliesCost).add(productSamplingCost).add(personalBuyCost);

            //活动推广费
            BigDecimal campaignPromotionCost = BigDecimal.ZERO;

            //销售费用合计
            BigDecimal totalSalesCost = supermarketCostSubtotal.add(employeeCost).add(lossSubtotal)
                    .add(supermarketSetupSubtotal).add(campaignPromotionCost);
            table.addToLastRow("totalSalesCost", Cell.builder().value(totalSalesCost).build());

            //直接费用
            BigDecimal directFulfillmentCost = BigDecimal.ZERO;
            //分摊费用
            BigDecimal allocatedFulfillmentCost = BigDecimal.ZERO;
            //小计
            BigDecimal fulfillmentCostsSubtotal = directFulfillmentCost.add(allocatedFulfillmentCost);

            //运营毛利
            BigDecimal operatingGrossProfit = grossProfitAmount.subtract(totalSalesCost).subtract(fulfillmentCostsSubtotal)
                    .setScale(2, RoundingMode.HALF_UP);
            table.addToLastRow("operatingGrossProfit", Cell.builder().value(operatingGrossProfit).build());

            //运营毛利率
            BigDecimal operatingGrossProfitMargin = salesAmount.compareTo(BigDecimal.ZERO) <= 0 ? BigDecimal.ZERO :
                    operatingGrossProfit.divide(salesAmount, 4, RoundingMode.HALF_UP)
                            .multiply(new BigDecimal("100")).setScale(2, RoundingMode.HALF_UP);
            table.addToLastRow("operatingGrossProfitMargin", Cell.builder().value(operatingGrossProfitMargin).build());
        }

        // 设置分页信息
        table.setPageSize((long) records.size());
        table.setTotalPage(1L);
        table.setCurrentPage(1L);
        table.setTotal((long) records.size());

        // 构建返回结果
        DataFormVO dataFormVO = new DataFormVO();
        dataFormVO.setTable(table);

        return dataFormVO;
    }

    private void buildSalesReportTitle(Table table, List<String> channelCodeIds) {

        // 添加表头
        table
                .addMetricCodeColumnFixed(MetricCodeEnum.CHANNEL.getCode())
                .addMetricCodeColumnFixed(MetricCodeEnum.AREA.getCode())
                .addMetricCodeColumnFixed(MetricCodeEnum.CUSTOMER.getCode())
                .addMetricCodeColumnFixed(MetricCodeEnum.CUSTOMER_OWNER.getCode());

        //销售毛利
        Column amount = Column.builder().key("salesAmountGlobal").title("销售毛利").build()
                .addChild(Column.targetColumn(TargetDataTypeEnum.SALES_VALID_AMOUNT.getCode()))
                .addChild(Column.targetColumn(TargetDataTypeEnum.COST_VALID_AMOUNT.getCode()))
//                .addChild(Column.builder().key("roundingOrFine").title("抹零罚款").isSort(true).build())
                .addChild(Column.targetColumn(TargetDataTypeEnum.GROSS_PROFIT_VALID_AMOUNT.getCode()))
                .addChild(Column.targetColumn(TargetDataTypeEnum.GROSS_PROFIT_VALID_MARGIN.getCode()))
                .addChild(Column.builder().key("produceName").title("更新时间").build());

        table.addToFirstColumn(amount);

        if (channelCodeIds.contains(MetricCodeIdEnum.CHANNEL_JOINT_SUPERMARKET.getCode())
                || channelCodeIds.contains(MetricCodeIdEnum.CHANNEL_DIRECT.getCode())) {
            //商超合同费用
            Column supermarket = Column.builder().key("supermarketGlobal").title("商超合同费用").build()
                    .addChild(Column.builder().key("marketDeducePoint").title("销售扣点").isSort(true).toLocaleString(true).build())
//                    .addChild(Column.builder().key("fixedConsumablesCost").title("固定耗材费用").isSort(true).build())
//                    .addChild(Column.builder().key("guideManagementCost").title("导购管理费").isSort(true).build())
//                    .addChild(Column.builder().key("electricityCost").title("电费").isSort(true).build())
//                    .addChild(Column.builder().key("deliveryCost").title("配送费").isSort(true).build())
//                    .addChild(Column.builder().key("promotionCost").title("促销费").isSort(true).build())
//                    .addChild(Column.builder().key("marketingCost").title("推广费").isSort(true).build())
//                    .addChild(Column.builder().key("slottingFee").title("进场费").isSort(true).build())
//                    .addChild(Column.builder().key("otherCosts").title("其他费用").isSort(true).build())
                    .addChild(Column.builder().key("supermarketCostSubtotal").title("小计").isSort(true).toLocaleString(true).build());
            table.addToFirstColumn(supermarket);

//            table.addToFirstColumn(Column.builder().key("employeeCost").title("员工社保").isSort(true).build());
        }

        if (channelCodeIds.contains(MetricCodeIdEnum.CHANNEL_JOINT_SUPERMARKET.getCode())) {
//            Column loss = Column.builder().key("lossGlobal").title("损耗").build()
//                    .addChild(Column.builder().key("clearanceLoss").title("出清损耗").isSort(true).build())
//                    .addChild(Column.builder().key("wastageLoss").title("损耗").isSort(true).build())
//                    .addChild(Column.builder().key("lossSubtotal").title("小计").isSort(true).build());
//            table.addToFirstColumn(loss);

//            Column marketSetup = Column.builder().key("marketSetupGlobal").title("超市基础配置费用").build()
//                    .addChild(Column.builder().key("freezerCost").title("冰柜折旧").isSort(true).build())
//                    .addChild(Column.builder().key("basicSuppliesCost").title("基础物料").isSort(true).build())
//                    .addChild(Column.builder().key("productSamplingCost").title("试吃物料").isSort(true).build())
//                    .addChild(Column.builder().key("personalBuyCost").title("自购物料").isSort(true).build())
//                    .addChild(Column.builder().key("supermarketSetupSubtotal").title("小计").isSort(true).build());
//            table.addToFirstColumn(marketSetup);

//            table.addToFirstColumn(Column.builder().key("campaignPromotionCost").title("活动推广费").isSort(true).build());
        }


        if (channelCodeIds.contains(MetricCodeIdEnum.CHANNEL_JOINT_SUPERMARKET.getCode())
                || channelCodeIds.contains(MetricCodeIdEnum.CHANNEL_DIRECT.getCode())) {
            table.addToFirstColumn(Column.builder().key("totalSalesCost").title("销售费用合计").isSort(true).toLocaleString(true).build());
        }

        Column fulfill = Column.builder().key("fulfill").title("履约费用").build()
                .addChild(Column.builder().key("directFulfillmentCost").title("直接费用").isSort(true).toLocaleString(true).build())
                .addChild(Column.builder().key("allocatedFulfillmentCost").title("分摊费用").isSort(true).toLocaleString(true).build())
                .addChild(Column.builder().key("fulfillmentCostsSubtotal").title("小计").isSort(true).toLocaleString(true).build());
        table.addToFirstColumn(fulfill);

        table.addToFirstColumn(Column.builder().key("operatingGrossProfit").title("运营毛利").isSort(true).toLocaleString(true).build());
        table.addToFirstColumn(Column.builder().key("operatingGrossProfitMargin").title("运营毛利率").isSort(true).percent(true).build());
    }

    @Override
    public Map<String, BigDecimal> queryActualValue(Long tenantId, String metricCode, Collection<String> participateIds, TimeCondition timeCondition) {
        if (tenantId == null || timeCondition == null) {
            return Maps.newHashMap();
        }
        Map<String, BigDecimal> result = Maps.newHashMap();
        //查公司
        if (MetricCodeEnum.ALLCOMPANY.getCode().equals(metricCode)) {
            result.put(null, _queryAllCompany(tenantId, timeCondition));
            return result;
        }

        Set<String> ids = Sets.newHashSet(participateIds);
        Map<String, Set<String>> childrenMapping = Maps.newHashMap();
        //查部门-全子部门均查询
        if (MetricCodeEnum.DEPARTMENT.getCode().equals(metricCode) && !ids.isEmpty()) {
            List<Long> list = participateIds.stream().map(Long::valueOf).toList();
            SingleResponse<Map<Long, List<Long>>> departResponse = departmentService.batchGetSubDeptIds(tenantId, list);
            if (departResponse != null && departResponse.getData() != null) {
                departResponse.getData().forEach((departId, subDeptIds) -> {
                    if (CollectionUtils.isNotEmpty(subDeptIds)) {
                        for (Long subDeptId : subDeptIds) {
                            String departIdStr = departId.toString();
                            String subDeptIdString = subDeptId.toString();
                            childrenMapping.computeIfAbsent(subDeptIdString, k -> new HashSet<>()).add(departIdStr);
                            ids.add(subDeptIdString);
                        }
                    }
                });
            }
        }

        //对于不支持的 metricCode，设置默认值
        String conditionField = MetricCodeEnum.metricCodeMapping(metricCode);
        if (conditionField == null) {
            return result;
        }

        //查指标
        List<Map<String, Object>> resultList;
        int tableType = timeCondition.tableType();
        if (tableType == 1) { // 月度数据
            resultList = _queryStringFromMonth(tenantId, conditionField, ids, timeCondition);
        } else if (tableType == 2) { // 日度数据
            resultList = _queryStringFromDay(tenantId, conditionField, ids, timeCondition);
        } else {
            return result;
        }


        for (Map<String, Object> row : resultList) {
            if (row != null && row.containsKey(conditionField)) {
                String fieldValue = row.get(conditionField).toString();
                BigDecimal totalAmount = new BigDecimal(row.getOrDefault("totalAmount", "0").toString());
                if (childrenMapping.containsKey(fieldValue)) {
                    for (String parentId : childrenMapping.get(fieldValue)) {
                        BigDecimal existValue = result.getOrDefault(parentId, BigDecimal.ZERO);
                        existValue = existValue.add(totalAmount);
                        result.put(parentId, existValue);
                    }
                } else {
                    result.put(fieldValue, totalAmount);
                }
            }
        }
        return result;
    }

    @Override
    public PageResponse<Map<String, Object>> batchActualValue(DataFormQueryVO dataFormQueryVO) {
        // 验证参数
        validate(dataFormQueryVO);
        //合计
        dataFormQueryVO.setTrend(false);

        // 表格查询 - 当前数据
        List<Map<String, Object>> tableRecords = _batchActualValue(dataFormQueryVO, 0);
        if (tableRecords == null) {
            return null;
        }

        // 计算总计数据用于占比计算
        List<Map<String, Object>> actualValue = _batchActualValue(dataFormQueryVO, 1);
        Map<String, Object> totalData = Optional.ofNullable(actualValue).map(list -> list.get(0)).orElse(Maps.newHashMap());

        // 计算环比数据
        TimeCondition momTimeCondition = dataFormQueryVO.getTimeCondition().copy().initMom();
        Map<String, Map<String, Object>> momData = _calculatePreData(dataFormQueryVO, momTimeCondition, 2);

        // 计算同比数据
        TimeCondition yoyTimeCondition = dataFormQueryVO.getTimeCondition().copy().initYoy();
        Map<String, Map<String, Object>> yoyData = _calculatePreData(dataFormQueryVO, yoyTimeCondition, 3);

        // 结果计算
        enhanceStaticsRecords(tableRecords, totalData, momData, yoyData, dataFormQueryVO);

        //内存分页排序
        PageResponse<Map<String, Object>> response = processResponse(tableRecords, dataFormQueryVO);
        // 查询中文名称
        batchMapIdToNames(response.getData(), dataFormQueryVO.getTenantId());

        return response;
    }


    private <T> void statistics_buildWrapper(QueryWrapper<T> wrapper, DataFormQueryVO dataFormQueryVO, List<String> selectDataColumns, int queryType) {
        LinkedHashMap<String, List<String>> queryOptions = dataFormQueryVO.getQueryOptions();
        //groupBy
        if (queryType != 1) {
            queryOptions.keySet().stream().map(MetricCodeEnum::metricCodeMapping).forEach(wrapper::groupBy);
        }
        //查询条件赋值
        queryOptions.forEach((key, value) -> wrapperValueSearch(wrapper, key, value));
        //添加条件字段
        List<String> selectColumns = queryType == 1 ? Lists.newArrayList() : queryOptions.keySet().stream().map(MetricCodeEnum::metricCodeMapping).collect(Collectors.toList());
        //添加查询字段
        selectColumns.addAll(selectDataColumns);

        if (!selectColumns.contains("produceId")
                && !selectColumns.contains("produceName")
                && !selectColumns.contains("produceOemFlag")
                && !selectColumns.contains("produceTypeId")) {
            //月表、汇总数据
            if (dataFormQueryVO.getTimeCondition().tableType() == 1 && TargetDataTypeEnum.isQueryValidAmountData(dataFormQueryVO.getTargetDataTypes())) {
                wrapper.eq("produceId", AMOUNT_VALID_SUMMARY_GOODS_ID);
                //查询条件带客户的时候，额外展示数据更新时间
                if (queryOptions.containsKey(MetricCodeEnum.CUSTOMER.getCode()) && !queryOptions.containsKey(MetricCodeEnum.CUSTOMER_NAME.getCode())) {
                    selectColumns.add("produceName");
                    wrapper.groupBy("produceName");
                }
            } else {
                //查询条件中不带produce，查询全店合计
                wrapper.eq("produceId", SUMMARY_GOODS_ID);
            }
        } else {
            //查询商品时，过滤全店
            wrapper.gt("produceId", SUMMARY_GOODS_ID);
        }

        wrapper.select(selectColumns)
                .between("visit_date", dataFormQueryVO.getTimeCondition().getStartDate(), dataFormQueryVO.getTimeCondition().getEndDate())
                .eq("tenantId", dataFormQueryVO.getTenantId())
                .eq("isDeleted", 0)
        ;
    }

    @Override
    public PageResponse<Map<String, Object>> batchTrendValue(DataFormQueryVO dataFormQueryVO) {
        List<Map<String, Object>> tableRecords = _batchTrendValue(dataFormQueryVO);
        if (tableRecords == null) {
            return null;
        }
        //趋势默认时间倒序
        if (dataFormQueryVO.getSortBy() == null) dataFormQueryVO.setSortBy("visit_date");
        //内存排序分页
        PageResponse<Map<String, Object>> response = processResponse(tableRecords, dataFormQueryVO);
        // 查询中文名称
        batchMapIdToNames(response.getData(), dataFormQueryVO.getTenantId());
        return response;
    }

    @Override
    public Chart<String, Cell> queryChart(DataFormQueryVO dataFormQueryVO) {
        //趋势
        dataFormQueryVO.setTrend(true);
        //先查询
        List<Map<String, Object>> tableRecords = _batchTrendValue(dataFormQueryVO);
        if (CollectionUtils.isEmpty(tableRecords) || tableRecords.get(0) == null) {
            return null;
        }
        return buildChart(dataFormQueryVO, tableRecords);
    }

    private List<Map<String, Object>> _batchTrendValue(DataFormQueryVO dataFormQueryVO) {
        // 验证参数
        validate(dataFormQueryVO);
        //趋势
        dataFormQueryVO.setTrend(true);

        //当前数据
        List<Map<String, Object>> tableRecords = _batchTrendValueType(dataFormQueryVO, 0);
        if (CollectionUtils.isEmpty(tableRecords) || tableRecords.get(0) == null) {
            return null;
        }

        TimeCondition momTimeCondition = dataFormQueryVO.getTimeCondition().copy().initMomTrend();
        Map<String, Map<String, Object>> momData = _calculatePreData(dataFormQueryVO, momTimeCondition, 2);

        // 计算同比数据
        TimeCondition yoyTimeCondition = dataFormQueryVO.getTimeCondition().copy().initYoy();
        Map<String, Map<String, Object>> yoyData = _calculatePreData(dataFormQueryVO, yoyTimeCondition, 3);

        yoyData.putAll(momData);
        //填充数据
        enhanceTrendRecords(tableRecords, yoyData, dataFormQueryVO);

        return tableRecords;
    }


    @Override
    public Map<Long, BigDecimal> queryActualValueForUser(Long tenantId, Collection<Long> userIds, TimeCondition timeCondition) {
        if (tenantId == null || userIds == null || userIds.isEmpty() || timeCondition == null) {
            return Maps.newHashMap();
        }
        Map<Long, BigDecimal> result = Maps.newHashMap();

        List<Map<String, Object>> resultList;
        int tableType = timeCondition.tableType();
        if (tableType == 1) { // 月度数据
            resultList = _queryLongFromMonth(tenantId, "salesId", userIds, timeCondition);
        } else if (tableType == 2) { // 日度数据
            resultList = _queryLongFromDay(tenantId, "salesId", userIds, timeCondition);
        } else {
            return result;
        }

        for (Map<String, Object> row : resultList) {
            if (row != null && row.containsKey("salesId")) {
                result.put(Long.parseLong(row.get("salesId").toString()),
                        new BigDecimal(row.getOrDefault("totalAmount", "0").toString()));
            }
        }
        return result;
    }

    /**
     * 趋势查询 ，无占比查询
     */
    private List<Map<String, Object>> _batchTrendValueType(DataFormQueryVO dataFormQueryVO, int queryType) {
        TimeCondition timeCondition = dataFormQueryVO.getTimeCondition();

        List<String> selectDataColumns = TargetDataTypeEnum.listByQueryType(dataFormQueryVO.getTargetDataTypes(), queryType);
        if (selectDataColumns.isEmpty()) return null;

        if (timeCondition.getPeriodType().equals(PeriodTypeEnum.CUSTOM_DAY.getCode())) {
            QueryWrapper<st_trd_customer_produce_day_info> wrapper = new QueryWrapper<>();
            _buildWrapper(wrapper, dataFormQueryVO, selectDataColumns);
            return st_trd_customer_produce_day_infoRepository.listMaps(wrapper);
        } else {
            QueryWrapper<st_trd_customer_produce_month_info> wrapper = new QueryWrapper<>();
            _buildWrapper(wrapper, dataFormQueryVO, selectDataColumns);
            return st_trd_customer_produce_month_infoRepository.listMaps(wrapper);
        }
    }

    private <T> void _buildWrapper(QueryWrapper<T> wrapper, DataFormQueryVO dataFormQueryVO, List<String> selectDataColumns) {
        //groupBy
        dataFormQueryVO.getQueryOptions().keySet().stream().map(MetricCodeEnum::metricCodeMapping).forEach(wrapper::groupBy);
        //查询条件赋值
        dataFormQueryVO.getQueryOptions().forEach((key, value) -> wrapperValueSearch(wrapper, key, value));
        //条件列
        List<String> selectColumns = dataFormQueryVO.getQueryOptions().keySet().stream().map(MetricCodeEnum::metricCodeMapping).collect(Collectors.toList());
        //日期列
        selectColumns.add("visit_date");
        //数据列
        selectColumns.addAll(selectDataColumns);
        wrapper.select(selectColumns)
                .groupBy("visit_date")
                .between("visit_date", dataFormQueryVO.getTimeCondition().getStartDate(), dataFormQueryVO.getTimeCondition().getEndDate())
                .eq("tenantId", dataFormQueryVO.getTenantId())
                .eq("isDeleted", 0)
                .ne("produceId", -1)  //过滤无效数据
        ;

        if (!selectColumns.contains("produceId")
                && !selectColumns.contains("produceName")
                && !selectColumns.contains("produceOemFlag")
                && !selectColumns.contains("produceTypeId")) {
            //查询条件中不带produce，查询全店合计
            wrapper.eq("produceId", SUMMARY_GOODS_ID);
        } else {
            //查询商品时，过滤全店
            wrapper.gt("produceId", SUMMARY_GOODS_ID);
        }
    }


    private List<Map<String, Object>> _queryLongFromMonth(Long tenantId, String field, Collection<Long> participates, TimeCondition timeCondition) {
        // 构建批量查询条件
        QueryWrapper<st_trd_customer_produce_month_info> queryWrapper = new QueryWrapper<>();
        queryWrapper.in(field, participates)
                .between("visit_date", timeCondition.getStartDate(), timeCondition.getEndDate())
                .eq("produceId", SUMMARY_GOODS_ID) // 取合计计算
                .eq("tenantId", tenantId)
                .eq("isDeleted", 0)
                .groupBy(field)
                .select("SUM(salesAmount) AS totalAmount", field);

        return st_trd_customer_produce_month_infoRepository.getBaseMapper().selectMaps(queryWrapper);
    }

    private List<Map<String, Object>> _queryLongFromDay(Long tenantId, String field, Collection<Long> participates, TimeCondition timeCondition) {
        // 构建批量查询条件
        QueryWrapper<st_trd_customer_produce_day_info> queryWrapper = new QueryWrapper<>();
        queryWrapper.in(field, participates)
                .between("visit_date", timeCondition.getStartDate(), timeCondition.getEndDate())
                .eq("produceId", SUMMARY_GOODS_ID) // 取合计计算
                .eq("tenantId", tenantId)
                .eq("isDeleted", 0)
                .groupBy(field)
                .select("SUM(salesAmount) AS totalAmount", field);

        return st_trd_customer_produce_day_infoRepository.getBaseMapper().selectMaps(queryWrapper);
    }

    private List<Map<String, Object>> _queryStringFromMonth(Long tenantId, String field, Collection<String> participates, TimeCondition timeCondition) {
        // 构建批量查询条件
        QueryWrapper<st_trd_customer_produce_month_info> queryWrapper = new QueryWrapper<>();
        queryWrapper.in(field, participates)
                .between("visit_date", timeCondition.getStartDate(), timeCondition.getEndDate())
                .eq("produceId", SUMMARY_GOODS_ID) // 取合计计算
                .eq("tenantId", tenantId)
                .eq("isDeleted", 0)
                .groupBy(field)
                .select("SUM(salesAmount) AS totalAmount", field);

        return st_trd_customer_produce_month_infoRepository.getBaseMapper().selectMaps(queryWrapper);
    }

    private List<Map<String, Object>> _queryStringFromDay(Long tenantId, String field, Collection<String> participates, TimeCondition timeCondition) {
        // 构建批量查询条件
        QueryWrapper<st_trd_customer_produce_day_info> queryWrapper = new QueryWrapper<>();
        queryWrapper.in(field, participates)
                .between("visit_date", timeCondition.getStartDate(), timeCondition.getEndDate())
                .eq("produceId", SUMMARY_GOODS_ID) // 取合计计算
                .eq("tenantId", tenantId)
                .eq("isDeleted", 0)
                .groupBy(field)
                .select("SUM(salesAmount) AS totalAmount", field);

        return st_trd_customer_produce_day_infoRepository.getBaseMapper().selectMaps(queryWrapper);
    }

    // 全公司数据查询
    private BigDecimal _queryAllCompany(Long tenantId, TimeCondition timeCondition) {
        List<Map<String, Object>> resultList;
        int tableType = timeCondition.tableType();
        if (1 == tableType) {
            QueryWrapper<st_trd_customer_produce_month_info> queryWrapper = new QueryWrapper<>();
            queryWrapper.between("visit_date", timeCondition.getStartDate(), timeCondition.getEndDate())
                    .eq("produceId", SUMMARY_GOODS_ID) // 取合计计算
                    .eq("tenantId", tenantId)
                    .eq("isDeleted", 0)
                    .select("SUM(salesAmount) AS totalAmount");
            resultList = st_trd_customer_produce_month_infoRepository.getBaseMapper().selectMaps(queryWrapper);
        } else if (2 == tableType) {
            QueryWrapper<st_trd_customer_produce_day_info> queryWrapper = new QueryWrapper<>();
            queryWrapper.between("visit_date", timeCondition.getStartDate(), timeCondition.getEndDate())
                    .eq("produceId", SUMMARY_GOODS_ID) // 取合计计算
                    .eq("tenantId", tenantId)
                    .eq("isDeleted", 0)
                    .select("SUM(salesAmount) AS totalAmount");
            resultList = st_trd_customer_produce_day_infoRepository.getBaseMapper().selectMaps(queryWrapper);
        } else {
            return BigDecimal.ZERO;
        }
        if (CollectionUtils.isNotEmpty(resultList) && resultList.get(0) != null) {
            return new BigDecimal(resultList.get(0).getOrDefault("totalAmount", "0").toString());
        }
        return BigDecimal.ZERO;
    }

    /**
     * 增强版的批量实际值查询，支持毛利额和毛利率计算
     * <p>
     * 不分页，内存排序
     */
    private List<Map<String, Object>> _batchActualValue(DataFormQueryVO dataFormQueryVO, int queryType) {
        TimeCondition timeCondition = dataFormQueryVO.getTimeCondition();

        List<String> selectDataColumns = TargetDataTypeEnum.listByQueryType(dataFormQueryVO.getTargetDataTypes(), queryType);
        if (selectDataColumns.isEmpty()) return null;

        List<Map<String, Object>> tableResultMaps;
        if (timeCondition.getPeriodType().equals(PeriodTypeEnum.CUSTOM_DAY.getCode())) {
            QueryWrapper<st_trd_customer_produce_day_info> wrapper = new QueryWrapper<>();
            statistics_buildWrapper(wrapper, dataFormQueryVO, selectDataColumns, queryType);
            tableResultMaps = st_trd_customer_produce_day_infoRepository.listMaps(wrapper);
        } else {
            QueryWrapper<st_trd_customer_produce_month_info> wrapper = new QueryWrapper<>();
            statistics_buildWrapper(wrapper, dataFormQueryVO, selectDataColumns, queryType);
            tableResultMaps = st_trd_customer_produce_month_infoRepository.listMaps(wrapper);
        }

        return tableResultMaps;
    }


    private Map<String, Map<String, Object>> _calculatePreData(DataFormQueryVO dataFormQueryVO, TimeCondition timeCondition, int queryType) {
        DataFormQueryVO preQueryVO = new DataFormQueryVO();
        preQueryVO.setQueryOptions(dataFormQueryVO.getQueryOptions());
        preQueryVO.setTimeCondition(timeCondition);
        preQueryVO.setTenantId(dataFormQueryVO.getTenantId());
        preQueryVO.setTargetDataTypes(dataFormQueryVO.getTargetDataTypes());
        preQueryVO.setTrend(dataFormQueryVO.getTrend());

        List<Map<String, Object>> preResults;
        if (preQueryVO.getTrend()) {
            preResults = _batchTrendValueType(preQueryVO, queryType);
        } else {
            preResults = _batchActualValue(preQueryVO, queryType);
        }

        // 转换为Map，以便快速查找
        return recordToMap(preResults, dataFormQueryVO.getQueryOptions().keySet(), preQueryVO.getTrend());
    }

    private Map<String, Map<String, Object>> recordToMap(List<Map<String, Object>> result, Set<String> queryOptions, boolean isTrend) {
        // 转换为Map，以便快速查找
        Map<String, Map<String, Object>> resultMap = Maps.newHashMap();
        if (CollectionUtils.isEmpty(result) || result.get(0) == null) {
            return resultMap;
        }
        for (Map<String, Object> record : result) {
            String key = isTrend ?
                    buildTrendRecordKey(record, queryOptions, record.get("visit_date").toString()) :
                    buildStaticRecordKey(record, queryOptions);
            resultMap.put(key, record);
        }
        return resultMap;
    }


    /**
     * 构建记录的唯一键，用于匹配环比和同比数据
     */
    private String buildStaticRecordKey(Map<String, Object> record, Set<String> queryOptions) {
        StringBuilder keyBuilder = new StringBuilder();
        for (String option : queryOptions) {
            String column = MetricCodeEnum.metricCodeMapping(option);
            if (column != null && record.containsKey(column)) {
                keyBuilder.append(column).append(":").append(record.get(column)).append("|");
            }
        }
        return keyBuilder.toString();
    }

    /**
     * 构建记录的唯一键，用于匹配环比和同比数据
     */
    private String buildTrendRecordKey(Map<String, Object> record, Set<String> queryOptions, String visit_date) {
        StringBuilder keyBuilder = new StringBuilder();
        keyBuilder.append("visit_date").append(":").append(visit_date).append("|");
        keyBuilder.append(buildStaticRecordKey(record, queryOptions));
        return keyBuilder.toString();
    }

    /**
     * 添加占比和增长率计算
     */
    private void enhanceStaticsRecords(List<Map<String, Object>> records,
                                       Map<String, Object> totalData,
                                       Map<String, Map<String, Object>> momData,
                                       Map<String, Map<String, Object>> yoyData,
                                       DataFormQueryVO dataFormQueryVO) {
        Set<String> queryKeys = dataFormQueryVO.getQueryOptions().keySet();
        List<String> tableTargetColumns = TargetDataTypeEnum.listTableColumns(dataFormQueryVO.getTargetDataTypes(), dataFormQueryVO.getTrend());
        BigDecimal totalSalesAmount = getBigDecimalValue(totalData, SALES_AMOUNT.getCode());
        BigDecimal totalGrossProfitAmount = getBigDecimalValue(totalData, GROSS_PROFIT_AMOUNT.getCode());

        for (Map<String, Object> record : records) {
            String recordKey = buildStaticRecordKey(record, queryKeys);

            // 当前数据
            BigDecimal salesAmount = getBigDecimalValue(record, SALES_AMOUNT.getCode());
            BigDecimal grossProfitAmount = getBigDecimalValue(record, GROSS_PROFIT_AMOUNT.getCode());
            BigDecimal grossProfitMargin = getBigDecimalValue(record, GROSS_PROFIT_MARGIN.getCode());

            // 计算占比
            if (tableTargetColumns.contains(SALES_AMOUNT_RATIO.getCode())) {
                record.put(SALES_AMOUNT_RATIO.getCode(), calculateRatio(salesAmount, totalSalesAmount));
            }
            if (tableTargetColumns.contains(GROSS_PROFIT_AMOUNT_RATIO.getCode())) {
                record.put(GROSS_PROFIT_AMOUNT_RATIO.getCode(), calculateRatio(grossProfitAmount, totalGrossProfitAmount));
            }


            // 计算环比增长
            Map<String, Object> momRecord = momData.getOrDefault(recordKey, Maps.newHashMap());
            if (tableTargetColumns.contains(SALES_AMOUNT_MOM_GROWTH.getCode())) {
                record.put(SALES_AMOUNT_MOM_GROWTH.getCode(),
                        calculateGrowthRate(salesAmount, getBigDecimalValue(momRecord, SALES_AMOUNT.getCode())));
            }
            if (tableTargetColumns.contains(GROSS_PROFIT_AMOUNT_MOM_GROWTH.getCode())) {
                record.put(GROSS_PROFIT_AMOUNT_MOM_GROWTH.getCode(),
                        calculateGrowthRate(grossProfitAmount, getBigDecimalValue(momRecord, GROSS_PROFIT_AMOUNT.getCode())));
            }
            if (tableTargetColumns.contains(GROSS_PROFIT_MARGIN_MOM_GROWTH.getCode())) {
                record.put(GROSS_PROFIT_MARGIN_MOM_GROWTH.getCode(),
                        calculateGrowthRate(grossProfitMargin, getBigDecimalValue(momRecord, GROSS_PROFIT_MARGIN.getCode())));
            }

            // 计算同比增长
            Map<String, Object> yoyRecord = yoyData.getOrDefault(recordKey, Maps.newHashMap());
            if (tableTargetColumns.contains(SALES_AMOUNT_YOY_GROWTH.getCode())) {
                record.put(SALES_AMOUNT_YOY_GROWTH.getCode(),
                        calculateGrowthRate(salesAmount, getBigDecimalValue(yoyRecord, SALES_AMOUNT.getCode())));
            }
            if (tableTargetColumns.contains(GROSS_PROFIT_AMOUNT_YOY_GROWTH.getCode())) {
                record.put(GROSS_PROFIT_AMOUNT_YOY_GROWTH.getCode(),
                        calculateGrowthRate(grossProfitAmount, getBigDecimalValue(yoyRecord, GROSS_PROFIT_AMOUNT.getCode())));
            }
            if (tableTargetColumns.contains(GROSS_PROFIT_MARGIN_YOY_GROWTH.getCode())) {
                record.put(GROSS_PROFIT_MARGIN_YOY_GROWTH.getCode(),
                        calculateGrowthRate(grossProfitMargin, getBigDecimalValue(yoyRecord, GROSS_PROFIT_MARGIN.getCode())));
            }
        }
    }

    /**
     * 添加占比和增长率计算
     */
    private void enhanceTrendRecords(List<Map<String, Object>> records,
                                     Map<String, Map<String, Object>> historyData,
                                     DataFormQueryVO dataFormQueryVO) {
        TimeCondition timeCondition = dataFormQueryVO.getTimeCondition();
        Set<String> queryKeys = dataFormQueryVO.getQueryOptions().keySet();
        List<String> tableTargetColumns = TargetDataTypeEnum.listTableColumns(dataFormQueryVO.getTargetDataTypes(), dataFormQueryVO.getTrend());

        Map<String, Map<String, Object>> recordMap = recordToMap(records, queryKeys, dataFormQueryVO.getTrend());
        for (Map<String, Object> record : records) {

            // 当前数据
            BigDecimal salesAmount = getBigDecimalValue(record, SALES_AMOUNT.getCode());
            BigDecimal grossProfitAmount = getBigDecimalValue(record, GROSS_PROFIT_AMOUNT.getCode());
            BigDecimal grossProfitMargin = getBigDecimalValue(record, GROSS_PROFIT_MARGIN.getCode());

            // 计算环比增长
            String momVisitDate = TimeCondition.getMomData(record.get("visit_date").toString(), timeCondition.getPeriodType());
            String momRecordKey = buildTrendRecordKey(record, queryKeys, momVisitDate);
            Map<String, Object> momRecord = recordMap.containsKey(momRecordKey) ? recordMap.get(momRecordKey) : historyData.get(momRecordKey);
            if (momRecord == null) momRecord = new HashMap<>();
            if (tableTargetColumns.contains(SALES_AMOUNT_MOM_GROWTH.getCode())) {
                record.put(SALES_AMOUNT_MOM_GROWTH.getCode(),
                        calculateGrowthRate(salesAmount, getBigDecimalValue(momRecord, SALES_AMOUNT.getCode())));
            }
            if (tableTargetColumns.contains(GROSS_PROFIT_AMOUNT_MOM_GROWTH.getCode())) {
                record.put(GROSS_PROFIT_AMOUNT_MOM_GROWTH.getCode(),
                        calculateGrowthRate(grossProfitAmount, getBigDecimalValue(momRecord, GROSS_PROFIT_AMOUNT.getCode())));
            }
            if (tableTargetColumns.contains(GROSS_PROFIT_MARGIN_MOM_GROWTH.getCode())) {
                record.put(GROSS_PROFIT_MARGIN_MOM_GROWTH.getCode(),
                        calculateGrowthRate(grossProfitMargin, getBigDecimalValue(momRecord, GROSS_PROFIT_MARGIN.getCode())));
            }

            // 计算同比增长
            String yoyVisitDate = TimeCondition.getYoyData(record.get("visit_date").toString(), timeCondition.getPeriodType());
            String yoyRecordKey = buildTrendRecordKey(record, queryKeys, yoyVisitDate);
            Map<String, Object> yoyRecord = historyData.containsKey(yoyRecordKey) ? historyData.get(yoyRecordKey) : recordMap.get(yoyRecordKey);
            if (yoyRecord == null) yoyRecord = new HashMap<>();
            if (tableTargetColumns.contains(SALES_AMOUNT_YOY_GROWTH.getCode())) {
                record.put(SALES_AMOUNT_YOY_GROWTH.getCode(),
                        calculateGrowthRate(salesAmount, getBigDecimalValue(yoyRecord, SALES_AMOUNT.getCode())));
            }
            if (tableTargetColumns.contains(GROSS_PROFIT_AMOUNT_YOY_GROWTH.getCode())) {
                record.put(GROSS_PROFIT_AMOUNT_YOY_GROWTH.getCode(),
                        calculateGrowthRate(grossProfitAmount, getBigDecimalValue(yoyRecord, GROSS_PROFIT_AMOUNT.getCode())));
            }
            if (tableTargetColumns.contains(GROSS_PROFIT_MARGIN_YOY_GROWTH.getCode())) {
                record.put(GROSS_PROFIT_MARGIN_YOY_GROWTH.getCode(),
                        calculateGrowthRate(grossProfitMargin, getBigDecimalValue(yoyRecord, GROSS_PROFIT_MARGIN.getCode())));
            }
        }
    }

    /**
     * 安全获取BigDecimal值
     */
    private BigDecimal getBigDecimalValue(Map<String, Object> map, String key) {
        if (map == null) {
            return BigDecimal.ZERO;
        }
        Object value = map.get(key);
        if (value == null) {
            return BigDecimal.ZERO;
        }
        if (value instanceof BigDecimal) {
            return (BigDecimal) value;
        }
        if (value instanceof Number) {
            return new BigDecimal(value.toString());
        }
        try {
            return new BigDecimal(value.toString());
        } catch (NumberFormatException e) {
            return BigDecimal.ZERO;
        }
    }

    /**
     * 计算占比（百分比）
     */
    private BigDecimal calculateRatio(BigDecimal value, BigDecimal total) {
        if (total == null || total.compareTo(BigDecimal.ZERO) == 0) {
            return BigDecimal.ZERO;
        }
        return value.divide(total, 4, RoundingMode.HALF_UP).multiply(new BigDecimal("100"));
    }

    /**
     * 计算增长率（百分比）
     */
    private BigDecimal calculateGrowthRate(BigDecimal current, BigDecimal previous) {
        if (previous == null || previous.compareTo(BigDecimal.ZERO) == 0) {
            return BigDecimal.ZERO;
        }
        return current.subtract(previous).divide(previous, 4, RoundingMode.HALF_UP).multiply(new BigDecimal("100"));
    }

    /**
     * 验证和处理targetDataTypes参数
     *
     * @param dataFormQueryVO 查询参数对象
     */
    private void validate(DataFormQueryVO dataFormQueryVO) {
        if (dataFormQueryVO == null) {
            throw new BizCustomException(400, "参数不能为空");
        }
        List<String> targetDataTypes = dataFormQueryVO.getTargetDataTypes();
        // 如果没有指定targetDataTypes，则不需要验证
        if (targetDataTypes == null || targetDataTypes.isEmpty()) {
            throw new BizCustomException(400, "必须选择需要查询的指标 ");
        }

        // 验证每个targetDataType是否有效
        for (String targetDataType : targetDataTypes) {
            if (!TargetDataTypeEnum.isValidCode(targetDataType)) {
                throw new BizCustomException(400, "无效的targetDataType: " + targetDataType);
            }
        }

        timeConditionCheck(dataFormQueryVO.getTimeCondition());
    }

    private PageResponse<Map<String, Object>> processResponse(List<Map<String, Object>> tableRecords, DataFormQueryVO dataFormQueryVO) {
        // 内存排序
        sortCalculatedFields(tableRecords, dataFormQueryVO);

        // 内存分页
        PageResponse<Map<String, Object>> result = PageResponse.of(dataFormQueryVO.getPageSize(), dataFormQueryVO.getPageIndex());
        result.setTotalCount(tableRecords.size());

        // 内存截断
        tableRecords = pageCalculatedFields(tableRecords, dataFormQueryVO);

        result.setData(tableRecords);

        return result;
    }

    /**
     * 对计算字段进行排序（应用层排序，用于复杂计算字段）
     *
     * @param records         数据记录列表
     * @param dataFormQueryVO 查询参数对象
     */
    private void sortCalculatedFields(List<Map<String, Object>> records, DataFormQueryVO dataFormQueryVO) {
        String sortBy = dataFormQueryVO.getSortBy();
        String sort = dataFormQueryVO.getSort();

        // 如果没有指定排序字段，则不进行排序
        if (StringUtils.isBlank(sortBy) || CollectionUtils.isEmpty(records) || records.get(0) == null) {
            return;
        }

        // 确定排序方向，默认为降序
        boolean isAsc = "asc".equalsIgnoreCase(sort);

        //数据排序的使用bigDecimal
        if (TargetDataTypeEnum.getByCode(sortBy) != null) {
            records.sort((record1, record2) -> {
                BigDecimal value1 = getBigDecimalValue(record1, sortBy);
                BigDecimal value2 = getBigDecimalValue(record2, sortBy);

                int compareResult = value1.compareTo(value2);

                // 如果是升序，直接返回比较结果；如果是降序，返回相反的结果
                return isAsc ? compareResult : -compareResult;
            });
        } else {
            String dataColumn = MetricCodeEnum.tableNameMappingDataColumn(sortBy);
            //使用string比较
            records.sort((record1, record2) -> {
                String value1 = getStringValue(record1, dataColumn);
                String value2 = getStringValue(record2, dataColumn);
                int compareResult = value1.compareTo(value2);
                return isAsc ? compareResult : -compareResult;
            });
        }
    }

    /**
     * 对计算字段进行排序
     *
     * @param records         数据记录列表
     * @param dataFormQueryVO 查询参数对象
     */
    private List<Map<String, Object>> pageCalculatedFields(List<Map<String, Object>> records, DataFormQueryVO dataFormQueryVO) {
        int pageIndex = dataFormQueryVO.getPageIndex();
        int pageSize = dataFormQueryVO.getPageSize();
        int offset = (pageIndex - 1) * pageSize;
        return records.subList(offset, Math.min(offset + pageSize, records.size()));
    }


}
