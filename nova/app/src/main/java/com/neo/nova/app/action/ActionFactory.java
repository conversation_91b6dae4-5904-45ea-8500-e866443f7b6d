package com.neo.nova.app.action;

import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.alibaba.fastjson.JSON;
import com.neo.nova.app.action.actionType.ActionSubTypeEnums;
import com.neo.nova.app.action.enums.ActionRelationEnums;
import com.neo.nova.app.action.enums.WorkOrderPriorityEnums;
import com.neo.nova.app.action.enums.WorkOrderStatusEnums;
import com.neo.nova.app.action.models.ActionBaseModel;
import com.neo.nova.app.action.models.ActionTaskModel;
import com.neo.nova.app.action.models.EventContent;
import com.neo.nova.app.service.WorkOrderEventService;
import com.neo.nova.app.util.WorkOrderUtil;
import com.neo.nova.domain.entity.WorkOrder;
import com.neo.nova.domain.entity.WorkOrderEvent;
import com.neo.nova.domain.gateway.IWorkOrderEventRepository;
import com.neo.nova.domain.gateway.IWorkOrderRepository;
import com.neo.session.SessionContextHolder;
import com.neo.timeout.client.api.TimeOutMsgOperateService;
import com.neo.timeout.client.domain.dto.SubscribeTimeOutBizMessageReqDTO;
import com.neo.user.client.notify.api.NotifyService;
import com.neo.user.client.notify.dto.TicketNotifyParam;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;
import java.util.Arrays;
import java.util.Calendar;

/*
 *             ,%%%%%%%%,
 *           ,%%/\%%%%/\%%
 *          ,%%%\c "" J/%%%
 * %.       %%%%/ o  o \%%%
 * `%%.     %%%%    _  |%%%
 *  `%%     `%%%%(__Y__)%%'
 *  //       ;%%%%`\-/%%%'
 * ((       /  `%%%%%%%'
 *  \\    .'          |
 *   \\  /       \  | |
 *    \\/         ) | |
 *     \         /_ | |__
 *     (___________))))))) 攻城狮
 *
 *
 * 用途说明:
 * 作者姓名: 竹笋
 * 创建时间: 2025/7/10 21:04
 */
@Slf4j
@Component
public class ActionFactory {

    @Resource
    NotifyService notifyService;

    @Resource
    IWorkOrderRepository workOrderRepository;

    @Resource
    IWorkOrderEventRepository workOrderEventRepository;

    @Resource
    TimeOutMsgOperateService timeOutMsgOperateService;

    /**
     * 执行动作
     *
     * @param actionBaseModel
     * @return
     */
    public Boolean execute(ActionBaseModel actionBaseModel) {

        log.info("执行任务 {}", actionBaseModel);

        WorkOrder workOrderById = workOrderRepository.getById(actionBaseModel.getWorkOrderId());
        if (workOrderById == null) {
            log.error("工单不存在 {}", actionBaseModel);
            return false;
        }

        if (actionBaseModel.getUserId() == null || actionBaseModel.getWorkOrderId() == null) {
            actionBaseModel.setExceptionMessage("参数校验失败：缺少必要参数");
            return false;
        }


        WorkOrderEvent workOrderEvent = WorkOrderUtil.convertToWorkOrderEvent(workOrderById);
        if(actionBaseModel.getExtraInfo() != null){
            workOrderEvent.setDescription(JSON.toJSONString(actionBaseModel.getExtraInfo()));
        }

        workOrderEvent.setType(workOrderById.getSubType());
        if(actionBaseModel.getSubType() != null){
            workOrderEvent.setType(actionBaseModel.getSubType());
        }
        if( workOrderEventRepository.save(workOrderEvent)){

            if(actionBaseModel.getSubType() == ActionSubTypeEnums.COMMENT.getType()){
                TicketNotifyParam ticketNotifyParam = new TicketNotifyParam();
                ticketNotifyParam.setTenantId(workOrderById.getTenantId());
                ticketNotifyParam.setTitle("留言");
                ticketNotifyParam.setReceiverIds(Arrays.asList(workOrderById.getExecutorId()));
                ticketNotifyParam.setTicketSerial(String.valueOf(workOrderById.getId()));
                ticketNotifyParam.setUrl("/pages/order/detail/index?id="+workOrderById.getId()+"&showAll=true");
                ticketNotifyParam.setContent("工单："+workOrderById.getTitle()+"有留言，请及时查看。");
                notifyService.sendTicketMessage(ticketNotifyParam);
            }

            if(workOrderById.getType() != ActionRelationEnums.CHECK_IN.getActionType()
                && actionBaseModel.getSubType() != ActionSubTypeEnums.COMMENT.getType()){
                WorkOrder workOrder = new WorkOrder();
                workOrder.setId(workOrderById.getId());
                workOrder.setUpdated(LocalDateTime.now());
                workOrder.setStatus(WorkOrderStatusEnums.COMPLETED.getCode());
                return workOrderRepository.updateById(workOrder);
            }else{
                return true;
            }

        }
        return false;

    }


    public Boolean createTask(ActionTaskModel actionTaskModel) {
//        ActionConfig actionConfig = RoleConfig.actionAuthConfig.get(actionTaskModel.getActionName());
//        if (actionConfig == null) {
//            return false;
//        }
        //先初始化主工单
        Long workOrderId = initWorkOrder(actionTaskModel);
        if (workOrderId == null) {
            log.error("初始化工单失败");
            return false;
        }
        //actionTaskModel.setWorkOrderId(workOrderId);
        return true;//actionConfig.getAction().createTask(actionTaskModel);
    }

    public Long initWorkOrder(ActionTaskModel actionTaskModel) {
        //先初始化主工单
        if(actionTaskModel.getActionType() == null){
            return null;
        }

        IWorkOrderRepository bean = SpringUtil.getBean(IWorkOrderRepository.class);
        WorkOrder workOrder = new WorkOrder();
        //todo 选择创建人
        workOrder.setCreatorId(SessionContextHolder.getUserId() == -1L ? 1L : SessionContextHolder.getUserId());
        workOrder.setExecutorId(actionTaskModel.getUserId());
        workOrder.setUpdaterId(1L);
        initTitle(actionTaskModel, workOrder);
        workOrder.setTenantId(actionTaskModel.getTenantId());
        if(actionTaskModel.getCustomerId() != null){
            workOrder.setBizId(String.valueOf(actionTaskModel.getCustomerId()));
        }
        Calendar calendar = Calendar.getInstance();
        int hour = calendar.get(Calendar.HOUR_OF_DAY);
        workOrder.setWorkOrderStartTime(LocalDateTimeUtil.beginOfDay(LocalDateTime.now()));
        workOrder.setWorkOrderEndTime(LocalDateTimeUtil.endOfDay(LocalDateTime.now()));
        //如果是打卡类型，需要判断一下打卡是上午还是下午的。
        if(actionTaskModel.getActionType() == ActionRelationEnums.CHECK_IN.getActionType() && hour >= 12){
            workOrder.setWorkOrderStartTime(LocalDateTimeUtil.beginOfDay(LocalDateTime.now()).plusHours(12));
        }
        if(actionTaskModel.getStartTime() != null){
            workOrder.setWorkOrderStartTime(actionTaskModel.getStartTime());
        }

        if(actionTaskModel.getEndTime() != null){
            workOrder.setWorkOrderEndTime(actionTaskModel.getEndTime());
        }

        workOrder.setStatus(WorkOrderStatusEnums.UNDERWAY.getCode());
        workOrder.setPriority(actionTaskModel.getPriority());
        workOrder.setTenantId(actionTaskModel.getTenantId());
        workOrder.setType(actionTaskModel.getActionType());
        if(actionTaskModel.getActionDetail() != null){
            workOrder.setDescription(JSON.toJSONString(actionTaskModel.getActionDetail()));
        }

        workOrder.setSubType(actionTaskModel.getWorkOrderDetailType());
        boolean save = bean.save(workOrder);
        if (!save) {
            log.error("初始化工单失败");
            return null;
        }



        //创建事件
        sendTimeOutMsg(workOrder);

        //加急工单发消息
        if(workOrder.getPriority() == WorkOrderPriorityEnums.URGENT.getCode()){
            TicketNotifyParam ticketNotifyParam = new TicketNotifyParam();
            ticketNotifyParam.setTenantId(workOrder.getTenantId());
            ticketNotifyParam.setTitle("工单加急");
            ticketNotifyParam.setReceiverIds(Arrays.asList(workOrder.getExecutorId()));
            ticketNotifyParam.setTicketSerial(String.valueOf(workOrder.getId()));
            ticketNotifyParam.setUrl("/pages/order/detail/index?id="+workOrder.getId());
            ticketNotifyParam.setContent("工单："+workOrder.getTitle()+"加急，请及时处理。");
            notifyService.sendTicketMessage(ticketNotifyParam);
        }


        return workOrder.getId();
    }

    private static void initTitle(ActionTaskModel actionTaskModel, WorkOrder workOrder) {
        if(StringUtils.isNotEmpty(actionTaskModel.getTitle())){
            workOrder.setTitle(actionTaskModel.getTitle());
        }else{
            workOrder.setTitle(ActionSubTypeEnums.map.get(actionTaskModel.getWorkOrderDetailType()));
            if(StringUtils.isNotBlank(actionTaskModel.getCustomerName())){
                workOrder.setTitle(workOrder.getTitle() + "-" + actionTaskModel.getCustomerName());
            }

            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
            //汇报加上时间
            if(actionTaskModel.getActionType() == ActionRelationEnums.REPORT.getActionType()
                    || actionTaskModel.getActionType() == ActionRelationEnums.CHECK_IN.getActionType()){
                workOrder.setTitle(workOrder.getTitle()+" "+ LocalDateTime.now().format(formatter));
            }
        }

    }

    //创建timeout消息
    private void sendTimeOutMsg(WorkOrder workOrder) {
        IWorkOrderEventRepository beanEvent = SpringUtil.getBean(IWorkOrderEventRepository.class);
        WorkOrderEvent workOrderEvent = WorkOrderUtil.convertToWorkOrderEvent(workOrder);
        EventContent eventContent = new EventContent();
        eventContent.setContent(ActionSubTypeEnums.CREATE.getActionDesc());
        workOrderEvent.setDescription(JSON.toJSONString(eventContent));
        workOrderEvent.setType(ActionSubTypeEnums.CREATE.getType());
        beanEvent.save(workOrderEvent);

        //创造完成之后还得去推一个临期的事件
        SubscribeTimeOutBizMessageReqDTO subscribeTimeOutBizMessageReqDTO = new SubscribeTimeOutBizMessageReqDTO();
        subscribeTimeOutBizMessageReqDTO.setBizId(workOrder.getId().toString());
        subscribeTimeOutBizMessageReqDTO.setBizType("WORK_ORDER_CREATE_EXPIRING");
        subscribeTimeOutBizMessageReqDTO.setBizData("");
        subscribeTimeOutBizMessageReqDTO.setUserId(1L);

        //根据不同的类型处理时间
        if(workOrder.getWorkOrderEndTime().compareTo(workOrder.getWorkOrderStartTime().plusDays(1)) <= 0){
            subscribeTimeOutBizMessageReqDTO.setHandleTime(workOrder.getWorkOrderEndTime().minusHours(3).toEpochSecond(ZoneOffset.of("+8")));
        }else{
            subscribeTimeOutBizMessageReqDTO.setHandleTime(workOrder.getWorkOrderEndTime().minusDays(1).toEpochSecond(ZoneOffset.of("+8")));
        }

        //临期的处理
        timeOutMsgOperateService.subscribeTimeOutBizMessage(subscribeTimeOutBizMessageReqDTO);

        //过期处理
        if(workOrder.getType() != ActionRelationEnums.VISITING_RECORD.getActionType()){
            subscribeTimeOutBizMessageReqDTO.setBizType("WORK_ORDER_CREATE_EXPIRED");
            subscribeTimeOutBizMessageReqDTO.setHandleTime(workOrder.getWorkOrderEndTime().toEpochSecond(ZoneOffset.of("+8")));
            timeOutMsgOperateService.subscribeTimeOutBizMessage(subscribeTimeOutBizMessageReqDTO);
        }
    }


}
