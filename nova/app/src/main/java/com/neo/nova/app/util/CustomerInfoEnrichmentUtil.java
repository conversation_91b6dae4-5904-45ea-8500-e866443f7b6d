package com.neo.nova.app.util;


import com.neo.nova.domain.dto.CustomerDTO;
import com.neo.nova.domain.entity.sqlserver.SynSlPamCustline;
import com.neo.nova.domain.gateway.sqlserver.SynSlPamCustlineRepository;
import com.neo.session.SessionContextHolder;
import com.neo.tagcenter.client.dto.TagLeafInfoDto;
import com.neo.tagcenter.client.param.BaseTagQueryOption;
import com.neo.tagcenter.client.param.BusinessTreeTagQueryParam;
import com.neo.tagcenter.client.rpc.BusinessTreeTagReadReadService;
import com.neo.user.client.userinfo.dto.UserInfoDTO;
import com.neo.user.client.userinfo.api.UserService;
import com.neo.api.MultiResponse;
import com.neo.api.SingleResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;


import java.util.*;
import java.util.stream.Collectors;

/**
 * 客户信息填充工具类
 * 用于填充CustomerDTO中的空字段，包括默认值设置和关联数据查询
 */
@Slf4j
@Component
public class CustomerInfoEnrichmentUtil {


    @Autowired(required = false)
    private SynSlPamCustlineRepository custlineRepository;


    @Autowired(required = false)
    private BusinessTreeTagReadReadService businessTreeTagReadReadService;

    @Autowired(required = false)
    private UserService userService;

    /**
     * 填充单个客户信息的空字段
     *
     * @param CustomerDTO 客户信息对象
     * @return 填充后的客户信息对象
     */
    public CustomerDTO enrichCustomerDTO(CustomerDTO CustomerDTO) {
        if (CustomerDTO == null) {
            return null;
        }

        List<CustomerDTO> customerList = Arrays.asList(CustomerDTO);
        enrichCustomerDTOBatch(customerList);
        return CustomerDTO;
    }

    /**
     * 批量填充客户信息的空字段
     *
     * @param CustomerDTOList 客户信息列表
     * @return 填充后的客户信息列表
     */
    public List<CustomerDTO> enrichCustomerDTOBatch(List<CustomerDTO> CustomerDTOList) {
        if (CollectionUtils.isEmpty(CustomerDTOList)) {
            return CustomerDTOList;
        }

        // 过滤掉null元素
        List<CustomerDTO> validCustomerDTOList = CustomerDTOList.stream()
                .filter(Objects::nonNull)
                .collect(Collectors.toList());

        if (validCustomerDTOList.isEmpty()) {
            log.warn("客户信息列表中没有有效的元素");
            return CustomerDTOList;
        }

        try {
            // 1. 填充基础默认值
//            fillDefaultValues(validCustomerDTOList);

            // 2. 填充关联数据
            fillRelatedData(validCustomerDTOList);

//            log.info("成功填充 {} 个客户信息的空字段", validCustomerDTOList.size());
            return CustomerDTOList;

        } catch (Exception e) {
            log.error("批量填充客户信息空字段失败", e);
            return CustomerDTOList;
        }
    }

    /**
     * 填充基础默认值
     */
    private void fillDefaultValues(List<CustomerDTO> CustomerDTOList) {
        Long currentTime = System.currentTimeMillis();
        Long currentUserId = getCurrentUserId();
        Long tenantId = getCurrentTenantId();

        for (CustomerDTO CustomerDTO : CustomerDTOList) {
            // 填充租户ID
            if (CustomerDTO.getTenantId() == null) {
                CustomerDTO.setTenantId(tenantId);
            }

            // 填充客户编码（如果为空，生成默认编码）
            if (!StringUtils.hasText(CustomerDTO.getCode())) {
                CustomerDTO.setCode(generateCustomerCode());
            }

            // 填充客户名称缩写（如果为空，使用客户名称）
            if (!StringUtils.hasText(CustomerDTO.getMnemoCode()) && StringUtils.hasText(CustomerDTO.getName())) {
                CustomerDTO.setMnemoCode(generateMnemoCode(CustomerDTO.getName()));
            }

            // 填充默认状态
            if (CustomerDTO.getStatus() == null) {
                CustomerDTO.setStatus(0); // 0正常
            }


            // 填充默认客户等级
            if (CustomerDTO.getLevel() == null) {
                CustomerDTO.setLevel(1); // 1普通客户
            }


        }
    }

    /**
     * 填充关联数据（参考SalesDataUpdateServiceImpl的实现）
     */
    private void fillRelatedData(List<CustomerDTO> CustomerDTOList) {
        Long tenantId = getCurrentTenantId();

        // 获取需要查询的ID集合
        Set<Long> customerTypeIds = CustomerDTOList.stream()
                .map(CustomerDTO::getCustomerTypeId)
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());

        Set<Long> customerAreaIds = CustomerDTOList.stream()
                .map(CustomerDTO::getCustomerAreaId)
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());

        Set<Long> adminRegionIds = CustomerDTOList.stream()
                .map(CustomerDTO::getAdminRegionId)
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());

        Set<Long> salesIds = CustomerDTOList.stream()
                .map(CustomerDTO::getSalesId)
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());
        Set<Long> customerLineIds = CustomerDTOList.stream()
                .map(CustomerDTO::getCustomerLineId)
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());


        // 查询关联数据（参考SalesDataUpdateServiceImpl的实现）
        Map<Long, TagLeafInfoDto> customerTypeTagMap = CollectionUtils.isEmpty(customerTypeIds) ?
                new HashMap<>() : getCustomerTypeTags(tenantId);
        Map<Long, TagLeafInfoDto> salesRegionTagMap = CollectionUtils.isEmpty(customerAreaIds) ?
                new HashMap<>() : getSalesRegionTags(tenantId);
        Map<Long, TagLeafInfoDto> adminRegionTagMap = CollectionUtils.isEmpty(adminRegionIds) ?
                new HashMap<>() : getAdminRegionTags(tenantId);
        Map<Long, TagLeafInfoDto> linkTagMap = CollectionUtils.isEmpty(customerLineIds) ?
                new HashMap<>() : getLinkTags(tenantId);
        Map<Long, UserInfoDTO> salesUserMap = getSalesUserMap(salesIds);

        // 填充名称字段
        for (CustomerDTO CustomerDTO : CustomerDTOList) {
            // 填充客户类型名称
            if (CustomerDTO.getCustomerTypeId() != null) {
                TagLeafInfoDto customerTypeTag = customerTypeTagMap.get(CustomerDTO.getCustomerTypeId());
                if (customerTypeTag != null) {
                    CustomerDTO.setCustomerTypeName(customerTypeTag.getName());

                }
            }

            // 填充销售区域名称
            if (CustomerDTO.getCustomerAreaId() != null) {
                TagLeafInfoDto salesRegionTag = salesRegionTagMap.get(CustomerDTO.getCustomerAreaId());
                if (salesRegionTag != null) {
                    CustomerDTO.setCustomerAreaName(salesRegionTag.getName());
                }
            }

            // 填充行政区域名称
            if (CustomerDTO.getAdminRegionId() != null) {
                TagLeafInfoDto adminRegionTag = adminRegionTagMap.get(CustomerDTO.getAdminRegionId());
                if (adminRegionTag != null) {
                    CustomerDTO.setAdminRegionName(adminRegionTag.getName());
                }
            }
            // 填充线路名称
            if (CustomerDTO.getCustomerLineId() != null) {
                TagLeafInfoDto linkTag = linkTagMap.get(CustomerDTO.getCustomerLineId());
                if (linkTag != null) {
                    CustomerDTO.setCustomerLineName(linkTag.getName());
                }
            }

            // 填充业务员姓名
            if (CustomerDTO.getSalesId() != null) {
                UserInfoDTO salesUser = salesUserMap.get(CustomerDTO.getSalesId());
                if (salesUser != null) {
                    CustomerDTO.setSalesName(salesUser.getUname());
                } else {
                    log.debug("未找到业务员信息: salesId={}", CustomerDTO.getSalesId());
                }
            }


        }
    }


    /**
     * 获取客户类型标签（参考SalesDataUpdateServiceImpl）
     */
    private Map<Long, TagLeafInfoDto> getCustomerTypeTags(Long tenantId) {
        try {
            if (businessTreeTagReadReadService == null) {
                return new HashMap<>();
            }

            BaseTagQueryOption option = new BaseTagQueryOption();
            option.setIncludeDeleted(true);
            option.setIncludeDisable(true);

            BusinessTreeTagQueryParam param = new BusinessTreeTagQueryParam();
            // 安全的Long转Integer
            if (tenantId > Integer.MAX_VALUE) {
                log.warn("租户ID超出Integer范围: {}", tenantId);
                return new HashMap<>();
            }
            param.setBusinessDomain(tenantId.intValue());

            MultiResponse<TagLeafInfoDto> response = businessTreeTagReadReadService
                    .queryCustomerTypeSetting(param, option);

            if (response.isSuccess() && response.getData() != null) {
                return response.getData().stream()
                        .collect(Collectors.toMap(
                                TagLeafInfoDto::getId,
                                tag -> tag,
                                (existing, replacement) -> existing
                        ));
            }
        } catch (Exception e) {
            log.warn("获取客户类型标签失败", e);
        }
        return new HashMap<>();
    }

    /**
     * 获取销售区域标签（参考SalesDataUpdateServiceImpl）
     */
    private Map<Long, TagLeafInfoDto> getSalesRegionTags(Long tenantId) {
        try {
            if (businessTreeTagReadReadService == null) {
                return new HashMap<>();
            }

            BaseTagQueryOption option = new BaseTagQueryOption();
            option.setIncludeDeleted(true);
            option.setIncludeDisable(true);

            BusinessTreeTagQueryParam param = new BusinessTreeTagQueryParam();
            // 安全的Long转Integer
            if (tenantId > Integer.MAX_VALUE) {
                log.warn("租户ID超出Integer范围: {}", tenantId);
                return new HashMap<>();
            }
            param.setBusinessDomain(tenantId.intValue());

            MultiResponse<TagLeafInfoDto> response = businessTreeTagReadReadService
                    .querySalesRegionSetting(param, option);

            if (response.isSuccess() && response.getData() != null) {
                return response.getData().stream()
                        .collect(Collectors.toMap(
                                TagLeafInfoDto::getId,
                                tag -> tag,
                                (existing, replacement) -> existing
                        ));
            }
        } catch (Exception e) {
            log.warn("获取销售区域标签失败", e);
        }
        return new HashMap<>();
    }

    /**
     * 获取行政区域标签（参考SalesDataUpdateServiceImpl）
     */
    private Map<Long, TagLeafInfoDto> getAdminRegionTags(Long tenantId) {
        try {
            if (businessTreeTagReadReadService == null) {
                return new HashMap<>();
            }

            BaseTagQueryOption option = new BaseTagQueryOption();
            option.setIncludeDeleted(true);
            option.setIncludeDisable(true);

            BusinessTreeTagQueryParam param = new BusinessTreeTagQueryParam();
            // 安全的Long转Integer
            if (tenantId > Integer.MAX_VALUE) {
                log.warn("租户ID超出Integer范围: {}", tenantId);
                return new HashMap<>();
            }
            param.setBusinessDomain(tenantId.intValue());

            MultiResponse<TagLeafInfoDto> response = businessTreeTagReadReadService
                    .queryAdministrativeRegionSetting(param, option);

            if (response.isSuccess() && response.getData() != null) {
                return response.getData().stream()
                        .collect(Collectors.toMap(
                                TagLeafInfoDto::getId,
                                tag -> tag,
                                (existing, replacement) -> existing
                        ));
            }
        } catch (Exception e) {
            log.warn("获取行政区域标签失败", e);
        }
        return new HashMap<>();
    }

    private Map<Long, TagLeafInfoDto> getLinkTags(Long tenantId) {
        try {
            if (businessTreeTagReadReadService == null) {
                return new HashMap<>();
            }

            BaseTagQueryOption option = new BaseTagQueryOption();
            option.setIncludeDeleted(true);
            option.setIncludeDisable(true);

            BusinessTreeTagQueryParam param = new BusinessTreeTagQueryParam();
            // 安全的Long转Integer
            if (tenantId > Integer.MAX_VALUE) {
                log.warn("租户ID超出Integer范围: {}", tenantId);
                return new HashMap<>();
            }
            param.setBusinessDomain(tenantId.intValue());

            MultiResponse<TagLeafInfoDto> response = businessTreeTagReadReadService
                    .queryDeliveryRouteSetting(param, option);

            if (response.isSuccess() && response.getData() != null) {
                return response.getData().stream()
                        .collect(Collectors.toMap(
                                TagLeafInfoDto::getId,
                                tag -> tag,
                                (existing, replacement) -> existing
                        ));
            }
        } catch (Exception e) {
            log.warn("获取线路标签失败", e);
        }
        return new HashMap<>();
    }

    /**
     * 获取业务员信息映射（参考SalesDataUpdateServiceImpl）
     */
    private Map<Long, UserInfoDTO> getSalesUserMap(Set<Long> salesIds) {
        if (CollectionUtils.isEmpty(salesIds)) {
            return new HashMap<>();
        }

        try {
            if (userService != null) {
                log.debug("查询业务员信息: salesIds={}", salesIds);
                SingleResponse<Map<Long, UserInfoDTO>> response = userService.queryMapByUserIds(new ArrayList<>(salesIds));
                if (response.isSuccess() && response.getData() != null) {
                    Map<Long, UserInfoDTO> resultMap = response.getData();
                    log.debug("获取到业务员信息: count={}, keys={}", resultMap.size(), resultMap.keySet());
                    // 验证返回的用户信息
                    for (Map.Entry<Long, UserInfoDTO> entry : resultMap.entrySet()) {
                        UserInfoDTO user = entry.getValue();
                        log.debug("业务员信息: userId={}, uname={}", user.getUserId(), user.getUname());
                    }
                    return resultMap;
                } else {
                    log.warn("获取业务员信息失败: success={}, errCode={}, errMessage={},data={}",
                            response.isSuccess(), response.getErrCode(), response.getErrMessage(), response.getData());
                }
            } else {
                log.warn("UserService未注入，无法获取业务员信息");
            }
        } catch (Exception e) {
            log.warn("获取业务员信息异常: salesIds={}", salesIds, e);
        }
        return new HashMap<>();
    }


    /**
     * 获取客户线路名称
     */
    private String getCustomerLineName(Long customerLineId) {
        if (customerLineId == null || custlineRepository == null) {
            return null;
        }

        try {
            // 安全的Long转Integer
            if (customerLineId > Integer.MAX_VALUE) {
                log.warn("客户线路ID超出Integer范围: {}", customerLineId);
                return null;
            }
            SynSlPamCustline custline = custlineRepository.getById(customerLineId.intValue());
            return custline != null ? custline.getCustlinename() : null;
        } catch (Exception e) {
            log.warn("获取客户线路名称失败: customerLineId={}", customerLineId, e);
            return null;
        }
    }

    /**
     * 生成客户编码
     */
    private String generateCustomerCode() {
        return "CUST" + System.currentTimeMillis();
    }

    /**
     * 生成客户名称缩写
     */
    private String generateMnemoCode(String customerName) {
        if (!StringUtils.hasText(customerName)) {
            return "";
        }
        // 简单的缩写生成逻辑，可以根据实际需求调整
        return customerName.length() > 10 ? customerName.substring(0, 10) : customerName;
    }

    /**
     * 获取当前用户ID
     */
    private Long getCurrentUserId() {
        try {
            return SessionContextHolder.getUserId();
        } catch (Exception e) {
            log.warn("获取当前用户ID失败，使用默认值", e);
            return 1L; // 默认系统用户ID
        }
    }

    /**
     * 获取当前租户ID
     */
    private Long getCurrentTenantId() {
        try {
            return 21L;
        } catch (Exception e) {
            log.warn("获取当前租户ID失败，使用默认值", e);
            return 1L; // 默认租户ID
        }
    }
}
