package com.neo.nova.app.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Maps;
import com.neo.api.MultiResponse;
import com.neo.api.SingleResponse;
import com.neo.nova.app.request.ComplexMetricListRequest;
import com.neo.nova.app.service.GoodsInfoService;
import com.neo.nova.app.service.MetricService;
import com.neo.nova.domain.StaticTenant;
import com.neo.nova.domain.dto.MetricDTO;
import com.neo.nova.domain.dto.MetricListDTO;
import com.neo.nova.domain.entity.CustomerInfo;
import com.neo.nova.domain.entity.GoodsInfo;
import com.neo.nova.domain.entity.Metric;
import com.neo.nova.domain.entity.MetricParticipate;
import com.neo.nova.domain.enums.CustomerLevelEnum;
import com.neo.nova.domain.enums.MetricCodeIdEnum;
import com.neo.nova.domain.enums.MetricCalcTypeEnum;
import com.neo.nova.domain.enums.MetricCodeEnum;
import com.neo.nova.domain.gateway.CustomerInfoRepository;
import com.neo.nova.domain.gateway.GoodsInfoRepository;
import com.neo.nova.domain.gateway.MetricParticipateRepository;
import com.neo.nova.domain.gateway.MetricRepository;
import com.neo.tagcenter.app.constants.TagDomainEnums;
import com.neo.tagcenter.client.dto.TagLeafInfoDto;
import com.neo.tagcenter.client.param.BaseTreeTagQueryOption;
import com.neo.tagcenter.client.param.TreeTagQueryOption;
import com.neo.tagcenter.client.rpc.TreeTagReadService;
import com.neo.user.client.tenant.api.DepartmentService;
import com.neo.user.client.tenant.dto.DepartmentInfoDTO;
import com.neo.user.client.tenant.dto.UserDepartmentDTO;
import com.neo.user.client.userinfo.api.UserService;
import com.neo.user.client.userinfo.dto.UserInfoDTO;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
public class MetricServiceImpl implements MetricService {

    @Resource
    private MetricRepository metricRepository;
    @Resource
    private MetricParticipateRepository metricParticipateRepository;
    @Resource
    private TreeTagReadService treeTagReadService;
    @Resource
    private CustomerInfoRepository customerInfoRepository;
    @Resource
    private UserService userService;
    @Resource
    private DepartmentService departmentService;
    @Resource
    private GoodsInfoRepository goodsInfoRepository;

    /**
     * 负责指标分页查询
     *
     * @param complexMetricListRequest
     * @return
     */
    @Override
    public MetricListDTO list(ComplexMetricListRequest complexMetricListRequest) {
        if (complexMetricListRequest.getMetricCode() == null) {
            return null;
        }
        LambdaQueryWrapper<Metric> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(Metric::getMetricCode, complexMetricListRequest.getMetricCode());

        if (complexMetricListRequest.getMetricName() != null) {
            wrapper.like(Metric::getMetricName, complexMetricListRequest.getMetricName());
        }

        wrapper.eq(Metric::getIsDeleted, 0);
        IPage<Metric> page = new Page<>(complexMetricListRequest.getPageIndex(), complexMetricListRequest.getPageSize());
        List<Metric> metrics = metricRepository.list(page, wrapper);
        List<MetricDTO> result = metrics.stream().map(metric -> {
            MetricDTO dto = new MetricDTO();
            BeanUtils.copyProperties(metric, dto);
            return dto;
        }).toList();

        MetricListDTO metricListDTO = new MetricListDTO();
        metricListDTO.setList(result);
        metricListDTO.setTotalCount(page.getTotal());
        return metricListDTO;
    }


    private Map<Long, CustomerInfo> queryCustomerInfo(Long tenantId, Set<Long> ids) {
        // 查询客户信息
        LambdaQueryWrapper<CustomerInfo> customerQuery = new LambdaQueryWrapper<>();
        customerQuery.in(CustomerInfo::getId, ids)
                .eq(CustomerInfo::getTenantId, tenantId)
                .eq(CustomerInfo::getIsDeleted, 0);

        List<CustomerInfo> customers = customerInfoRepository.list(customerQuery);
        return customers.stream()
                .collect(Collectors.toMap(CustomerInfo::getId, customer -> customer));
    }

    private Map<Long, GoodsInfo> queryGoodsInfo(Long tenantId, Set<Long> ids) {
        // 查询客户信息
        LambdaQueryWrapper<GoodsInfo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(GoodsInfo::getId, ids)
                .eq(GoodsInfo::getTenantId, tenantId)
                .eq(GoodsInfo::getIsDeleted, 0);

        List<GoodsInfo> goodsInfos = goodsInfoRepository.list(queryWrapper);
        return goodsInfos.stream()
                .collect(Collectors.toMap(GoodsInfo::getId, goodsInfo -> goodsInfo));
    }

    private Map<Long, String> queryUserInfo(Long tenantId, Set<Long> ids) {
        MultiResponse<UserInfoDTO> userInfoDTOMultiResponse = userService.queryByUserIds(new ArrayList<>(ids));
        if (userInfoDTOMultiResponse != null && userInfoDTOMultiResponse.getData() != null) {
            return userInfoDTOMultiResponse.getData().stream()
                    .collect(Collectors.toMap(UserInfoDTO::getUserId, UserInfoDTO::getUname, (k1, k2) -> k1));
        }
        return new HashMap<>();
    }


    private Map<Long, String> queryDepartmentInfo(Long tenantId, Set<Long> ids) {
        Map<Long, String> result = Maps.newHashMap();
        SingleResponse<Map<Long, DepartmentInfoDTO>> departmentDTOMultiResponse = departmentService.getDeptInfoMapByIds(tenantId, new ArrayList<>(ids));
        if (departmentDTOMultiResponse != null && departmentDTOMultiResponse.getData() != null) {
            departmentDTOMultiResponse.getData().forEach((k, v) -> result.put(k, v.getName()));
        }
        return result;
    }

    /**
     * 获取客户类型标签
     */
    private Map<Long, TagLeafInfoDto> queryTags(Long tenantId, String businessDomain, Set<Long> ids) {
        try {
            TreeTagQueryOption option = new TreeTagQueryOption();
            option.setIncludeDeleted(true);
            option.setIncludeDisable(true);

            option.setBusinessDomains(List.of(tenantId.intValue()));
            option.setTagDomains(List.of(businessDomain));
            option.setTagLeafIds(new ArrayList<>(ids));

            MultiResponse<TagLeafInfoDto> response = treeTagReadService.queryTagLeafInfo(option);

            if (response.isSuccess() && response.getData() != null) {
                return response.getData().stream()
                        .collect(Collectors.toMap(
                                TagLeafInfoDto::getId,
                                tag -> tag,
                                (existing, replacement) -> existing
                        ));
            }
        } catch (Exception e) {
            log.warn("获取标签失败,tenantId:{},businessDomain:{},ids:{}", tenantId, businessDomain, ids, e);
        }
        return new HashMap<>();
    }


    @Override
    public void init(Long tenantId) {
        //如果数据库中已存在，就不新增
        LambdaQueryWrapper<Metric> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Metric::getTenantId, tenantId);
        queryWrapper.eq(Metric::getMetricCode, MetricCodeIdEnum.CHANNEL_JOINT_SUPERMARKET.getParentCode().getCode());
        queryWrapper.eq(Metric::getMetricCodeId, MetricCodeIdEnum.CHANNEL_JOINT_SUPERMARKET.getCode());
        queryWrapper.eq(Metric::getIsDeleted, 0);
        List<Metric> exist = metricRepository.list(queryWrapper);
        if (CollectionUtils.isNotEmpty(exist)) {
            log.info("已存在渠道指标");
            return;
        }


        // MetricCodeEnum.CHANNEL
        {
            Metric joinMarket = new Metric();
            joinMarket.setTenantId(tenantId);
            joinMarket.setMetricCode(MetricCodeIdEnum.CHANNEL_JOINT_SUPERMARKET.getParentCode().getCode());
            joinMarket.setMetricCodeId(MetricCodeIdEnum.CHANNEL_JOINT_SUPERMARKET.getCode());
            joinMarket.setMetricName(MetricCodeIdEnum.CHANNEL_JOINT_SUPERMARKET.getName());
            joinMarket.setCalcType(0);
            joinMarket.setIsDeleted(0);
            joinMarket.setCreated(System.currentTimeMillis() / 1000);
            joinMarket.setCreatedBy(1L);
            joinMarket.setUpdated(System.currentTimeMillis() / 1000);
            joinMarket.setUpdatedBy(1L);
            metricRepository.save(joinMarket);
            BaseTreeTagQueryOption option = new BaseTreeTagQueryOption();
            option.setIncludeDisable(true);
            option.setQueryChild(true);
            MultiResponse<TagLeafInfoDto> tagResponse = treeTagReadService.queryTagLeafInfoByTagName(tenantId.intValue(), TagDomainEnums.CUSTOMER_TYPE.getCode(), "传统连锁商超（联营）", option);
            _generalParticipate(tenantId, joinMarket, MetricCodeIdEnum.CHANNEL_JOINT_SUPERMARKET, MetricCodeEnum.CUSTOMER_TYPE, MetricCalcTypeEnum.METRIC_PARTICIPATE_IN, tagResponse);

            Metric purchaseMarket = new Metric();
            purchaseMarket.setTenantId(tenantId);
            purchaseMarket.setMetricCode(MetricCodeIdEnum.CHANNEL_PURCHASE_SUPERMARKET.getParentCode().getCode());
            purchaseMarket.setMetricCodeId(MetricCodeIdEnum.CHANNEL_PURCHASE_SUPERMARKET.getCode());
            purchaseMarket.setMetricName(MetricCodeIdEnum.CHANNEL_PURCHASE_SUPERMARKET.getName());
            purchaseMarket.setCalcType(0);
            purchaseMarket.setIsDeleted(0);
            purchaseMarket.setCreated(System.currentTimeMillis() / 1000);
            purchaseMarket.setCreatedBy(1L);
            purchaseMarket.setUpdated(System.currentTimeMillis() / 1000);
            purchaseMarket.setUpdatedBy(1L);
            metricRepository.save(purchaseMarket);
            option = new BaseTreeTagQueryOption();
            option.setIncludeDisable(true);
            option.setQueryChild(true);
            tagResponse = treeTagReadService.queryTagLeafInfoByTagName(tenantId.intValue(), TagDomainEnums.CUSTOMER_TYPE.getCode(), "普通连锁商超（购销）", option);
            _generalParticipate(tenantId, purchaseMarket, MetricCodeIdEnum.CHANNEL_PURCHASE_SUPERMARKET, MetricCodeEnum.CUSTOMER_TYPE, MetricCalcTypeEnum.METRIC_PARTICIPATE_IN, tagResponse);


            Metric direct = new Metric();
            direct.setTenantId(tenantId);
            direct.setMetricCode(MetricCodeIdEnum.CHANNEL_DIRECT.getParentCode().getCode());
            direct.setMetricCodeId(MetricCodeIdEnum.CHANNEL_DIRECT.getCode());
            direct.setMetricName(MetricCodeIdEnum.CHANNEL_DIRECT.getName());
            direct.setCalcType(0);
            direct.setIsDeleted(0);
            direct.setCreated(System.currentTimeMillis() / 1000);
            direct.setCreatedBy(1L);
            direct.setUpdated(System.currentTimeMillis() / 1000);
            direct.setUpdatedBy(1L);
            metricRepository.save(direct);
            tagResponse = treeTagReadService.queryTagLeafInfoByTagName(tenantId.intValue(), TagDomainEnums.CUSTOMER_TYPE.getCode(), "自营", option);
            _generalParticipate(tenantId, direct, MetricCodeIdEnum.CHANNEL_DIRECT, MetricCodeEnum.CUSTOMER_TYPE, MetricCalcTypeEnum.METRIC_PARTICIPATE_IN, tagResponse);

            Metric dealer = new Metric();
            dealer.setTenantId(tenantId);
            dealer.setMetricCode(MetricCodeIdEnum.CHANNEL_DEALER.getParentCode().getCode());
            dealer.setMetricCodeId(MetricCodeIdEnum.CHANNEL_DEALER.getCode());
            dealer.setMetricName(MetricCodeIdEnum.CHANNEL_DEALER.getName());
            dealer.setCalcType(0);
            dealer.setIsDeleted(0);
            dealer.setCreated(System.currentTimeMillis() / 1000);
            dealer.setCreatedBy(1L);
            dealer.setUpdated(System.currentTimeMillis() / 1000);
            dealer.setUpdatedBy(1L);
            metricRepository.save(dealer);
            tagResponse = treeTagReadService.queryTagLeafInfoByTagName(tenantId.intValue(), TagDomainEnums.CUSTOMER_TYPE.getCode(), "经销商", option);
            _generalParticipate(tenantId, dealer, MetricCodeIdEnum.CHANNEL_DEALER, MetricCodeEnum.CUSTOMER_TYPE, MetricCalcTypeEnum.METRIC_PARTICIPATE_IN, tagResponse);


            Metric vip = new Metric();
            vip.setTenantId(tenantId);
            vip.setMetricCode(MetricCodeIdEnum.CHANNEL_VIP.getParentCode().getCode());
            vip.setMetricCodeId(MetricCodeIdEnum.CHANNEL_VIP.getCode());
            vip.setMetricName(MetricCodeIdEnum.CHANNEL_VIP.getName());
            vip.setCalcType(0);
            vip.setIsDeleted(0);
            vip.setCreated(System.currentTimeMillis() / 1000);
            vip.setCreatedBy(1L);
            vip.setUpdated(System.currentTimeMillis() / 1000);
            vip.setUpdatedBy(1L);
            metricRepository.save(vip);
            tagResponse = treeTagReadService.queryTagLeafInfoByTagName(tenantId.intValue(), TagDomainEnums.CUSTOMER_TYPE.getCode(), "传统连锁商超（联营）", option);
            _generalParticipate(tenantId, vip, MetricCodeIdEnum.CHANNEL_VIP, MetricCodeEnum.CUSTOMER_TYPE, MetricCalcTypeEnum.METRIC_PARTICIPATE_NOT_IN, tagResponse);
            tagResponse = treeTagReadService.queryTagLeafInfoByTagName(tenantId.intValue(), TagDomainEnums.CUSTOMER_TYPE.getCode(), "普通连锁商超（购销）", option);
            _generalParticipate(tenantId, vip, MetricCodeIdEnum.CHANNEL_VIP, MetricCodeEnum.CUSTOMER_TYPE, MetricCalcTypeEnum.METRIC_PARTICIPATE_NOT_IN, tagResponse);
            tagResponse = treeTagReadService.queryTagLeafInfoByTagName(tenantId.intValue(), TagDomainEnums.CUSTOMER_TYPE.getCode(), "自营", option);
            _generalParticipate(tenantId, vip, MetricCodeIdEnum.CHANNEL_VIP, MetricCodeEnum.CUSTOMER_TYPE, MetricCalcTypeEnum.METRIC_PARTICIPATE_NOT_IN, tagResponse);
            tagResponse = treeTagReadService.queryTagLeafInfoByTagName(tenantId.intValue(), TagDomainEnums.CUSTOMER_TYPE.getCode(), "经销商", option);
            _generalParticipate(tenantId, vip, MetricCodeIdEnum.CHANNEL_VIP, MetricCodeEnum.CUSTOMER_TYPE, MetricCalcTypeEnum.METRIC_PARTICIPATE_NOT_IN, tagResponse);
        }

        // MetricCodeEnum.AREA
        {
            Metric east = new Metric();
            east.setTenantId(tenantId);
            east.setMetricCode(MetricCodeIdEnum.AREA_EAST.getParentCode().getCode());
            east.setMetricCodeId(MetricCodeIdEnum.AREA_EAST.getCode());
            east.setMetricName(MetricCodeIdEnum.AREA_EAST.getName());
            east.setCalcType(0);
            east.setIsDeleted(0);
            east.setCreated(System.currentTimeMillis() / 1000);
            east.setCreatedBy(1L);
            east.setUpdated(System.currentTimeMillis() / 1000);
            east.setUpdatedBy(1L);
            metricRepository.save(east);
            BaseTreeTagQueryOption option = new BaseTreeTagQueryOption();
            option.setIncludeDisable(true);
            option.setQueryChild(true);
//            MultiResponse<TagLeafInfoDto> tagResponse = treeTagReadService.queryTagLeafInfoByTagName(tenantId.intValue(), TagDomainEnums.CUSTOMER_TYPE.getCode(), "传统连锁商超（联营）", option);
//            _generalParticipate(tenantId, east, MetricCodeIdEnum.SUPERMARKET_AREA_EAST, MetricCodeEnum.CUSTOMER_TYPE, MetricCalcTypeEnum.METRIC_PARTICIPATE_IN, tagResponse);
//            tagResponse = treeTagReadService.queryTagLeafInfoByTagName(tenantId.intValue(), TagDomainEnums.ADMINISTRATIVE_REGION.getCode(), "于都县", option);
//            _generalParticipate(tenantId, east, MetricCodeIdEnum.SUPERMARKET_AREA_EAST, MetricCodeEnum.CUSTOMER_ADMIN_REGION, MetricCalcTypeEnum.METRIC_PARTICIPATE_IN, tagResponse);
//            tagResponse = treeTagReadService.queryTagLeafInfoByTagName(tenantId.intValue(), TagDomainEnums.ADMINISTRATIVE_REGION.getCode(), "瑞金市", option);
//            _generalParticipate(tenantId, east, MetricCodeIdEnum.SUPERMARKET_AREA_EAST, MetricCodeEnum.CUSTOMER_ADMIN_REGION, MetricCalcTypeEnum.METRIC_PARTICIPATE_IN, tagResponse);
//            tagResponse = treeTagReadService.queryTagLeafInfoByTagName(tenantId.intValue(), TagDomainEnums.ADMINISTRATIVE_REGION.getCode(), "会昌县", option);
//            _generalParticipate(tenantId, east, MetricCodeIdEnum.SUPERMARKET_AREA_EAST, MetricCodeEnum.CUSTOMER_ADMIN_REGION, MetricCalcTypeEnum.METRIC_PARTICIPATE_IN, tagResponse);
//            tagResponse = treeTagReadService.queryTagLeafInfoByTagName(tenantId.intValue(), TagDomainEnums.ADMINISTRATIVE_REGION.getCode(), "信丰县", option);
//            _generalParticipate(tenantId, east, MetricCodeIdEnum.SUPERMARKET_AREA_EAST, MetricCodeEnum.CUSTOMER_ADMIN_REGION, MetricCalcTypeEnum.METRIC_PARTICIPATE_IN, tagResponse);
            //勤润设置改了，行政区域 = 东/南/西区域 ；
            MultiResponse<TagLeafInfoDto> tagResponse = treeTagReadService.queryTagLeafInfoByTagName(tenantId.intValue(), TagDomainEnums.ADMINISTRATIVE_REGION.getCode(), "东部", option);
            _generalParticipate(tenantId, east, MetricCodeIdEnum.AREA_EAST, MetricCodeEnum.CUSTOMER_ADMIN_REGION, MetricCalcTypeEnum.METRIC_PARTICIPATE_IN, tagResponse);


            Metric west = new Metric();
            west.setTenantId(tenantId);
            west.setMetricCode(MetricCodeIdEnum.AREA_WEST.getParentCode().getCode());
            west.setMetricCodeId(MetricCodeIdEnum.AREA_WEST.getCode());
            west.setMetricName(MetricCodeIdEnum.AREA_WEST.getName());
            west.setCalcType(0);
            west.setIsDeleted(0);
            west.setCreated(System.currentTimeMillis() / 1000);
            west.setCreatedBy(1L);
            west.setUpdated(System.currentTimeMillis() / 1000);
            west.setUpdatedBy(1L);
            metricRepository.save(west);
//            tagResponse = treeTagReadService.queryTagLeafInfoByTagName(tenantId.intValue(), TagDomainEnums.CUSTOMER_TYPE.getCode(), "传统连锁商超（联营）", option);
//            _generalParticipate(tenantId, west, MetricCodeIdEnum.SUPERMARKET_AREA_WEST, MetricCodeEnum.CUSTOMER_TYPE, MetricCalcTypeEnum.METRIC_PARTICIPATE_IN, tagResponse);
//            tagResponse = treeTagReadService.queryTagLeafInfoByTagName(tenantId.intValue(), TagDomainEnums.ADMINISTRATIVE_REGION.getCode(), "赣州市", option);
//            _generalParticipate(tenantId, west, MetricCodeIdEnum.SUPERMARKET_AREA_WEST, MetricCodeEnum.CUSTOMER_ADMIN_REGION, MetricCalcTypeEnum.METRIC_PARTICIPATE_IN, tagResponse);
//            tagResponse = treeTagReadService.queryTagLeafInfoByTagName(tenantId.intValue(), TagDomainEnums.ADMINISTRATIVE_REGION.getCode(), "赣县区", option);
//            _generalParticipate(tenantId, west, MetricCodeIdEnum.SUPERMARKET_AREA_WEST, MetricCodeEnum.CUSTOMER_ADMIN_REGION, MetricCalcTypeEnum.METRIC_PARTICIPATE_IN, tagResponse);
//            tagResponse = treeTagReadService.queryTagLeafInfoByTagName(tenantId.intValue(), TagDomainEnums.ADMINISTRATIVE_REGION.getCode(), "南康区", option);
//            _generalParticipate(tenantId, west, MetricCodeIdEnum.SUPERMARKET_AREA_WEST, MetricCodeEnum.CUSTOMER_ADMIN_REGION, MetricCalcTypeEnum.METRIC_PARTICIPATE_IN, tagResponse);
//            tagResponse = treeTagReadService.queryTagLeafInfoByTagName(tenantId.intValue(), TagDomainEnums.ADMINISTRATIVE_REGION.getCode(), "大余县", option);
//            _generalParticipate(tenantId, west, MetricCodeIdEnum.SUPERMARKET_AREA_WEST, MetricCodeEnum.CUSTOMER_ADMIN_REGION, MetricCalcTypeEnum.METRIC_PARTICIPATE_IN, tagResponse);
//            tagResponse = treeTagReadService.queryTagLeafInfoByTagName(tenantId.intValue(), TagDomainEnums.ADMINISTRATIVE_REGION.getCode(), "上犹县", option);
//            _generalParticipate(tenantId, west, MetricCodeIdEnum.SUPERMARKET_AREA_WEST, MetricCodeEnum.CUSTOMER_ADMIN_REGION, MetricCalcTypeEnum.METRIC_PARTICIPATE_IN, tagResponse);
//            tagResponse = treeTagReadService.queryTagLeafInfoByTagName(tenantId.intValue(), TagDomainEnums.ADMINISTRATIVE_REGION.getCode(), "崇义县", option);
//            _generalParticipate(tenantId, west, MetricCodeIdEnum.SUPERMARKET_AREA_WEST, MetricCodeEnum.CUSTOMER_ADMIN_REGION, MetricCalcTypeEnum.METRIC_PARTICIPATE_IN, tagResponse);
            tagResponse = treeTagReadService.queryTagLeafInfoByTagName(tenantId.intValue(), TagDomainEnums.ADMINISTRATIVE_REGION.getCode(), "西部", option);
            _generalParticipate(tenantId, west, MetricCodeIdEnum.AREA_WEST, MetricCodeEnum.CUSTOMER_ADMIN_REGION, MetricCalcTypeEnum.METRIC_PARTICIPATE_IN, tagResponse);

            Metric south = new Metric();
            south.setTenantId(tenantId);
            south.setMetricCode(MetricCodeIdEnum.AREA_SOUTH.getParentCode().getCode());
            south.setMetricCodeId(MetricCodeIdEnum.AREA_SOUTH.getCode());
            south.setMetricName(MetricCodeIdEnum.AREA_SOUTH.getName());
            south.setCalcType(0);
            south.setIsDeleted(0);
            south.setCreated(System.currentTimeMillis() / 1000);
            south.setCreatedBy(1L);
            south.setUpdated(System.currentTimeMillis() / 1000);
            south.setUpdatedBy(1L);
            metricRepository.save(south);
//            tagResponse = treeTagReadService.queryTagLeafInfoByTagName(tenantId.intValue(), TagDomainEnums.CUSTOMER_TYPE.getCode(), "传统连锁商超（联营）", option);
//            _generalParticipate(tenantId, south, MetricCodeIdEnum.SUPERMARKET_AREA_SOUTH, MetricCodeEnum.CUSTOMER_TYPE, MetricCalcTypeEnum.METRIC_PARTICIPATE_IN, tagResponse);
//            tagResponse = treeTagReadService.queryTagLeafInfoByTagName(tenantId.intValue(), TagDomainEnums.ADMINISTRATIVE_REGION.getCode(), "龙南市", option);
//            _generalParticipate(tenantId, south, MetricCodeIdEnum.SUPERMARKET_AREA_SOUTH, MetricCodeEnum.CUSTOMER_ADMIN_REGION, MetricCalcTypeEnum.METRIC_PARTICIPATE_IN, tagResponse);
//            tagResponse = treeTagReadService.queryTagLeafInfoByTagName(tenantId.intValue(), TagDomainEnums.ADMINISTRATIVE_REGION.getCode(), "全南县", option);
//            _generalParticipate(tenantId, south, MetricCodeIdEnum.SUPERMARKET_AREA_SOUTH, MetricCodeEnum.CUSTOMER_ADMIN_REGION, MetricCalcTypeEnum.METRIC_PARTICIPATE_IN, tagResponse);
//            tagResponse = treeTagReadService.queryTagLeafInfoByTagName(tenantId.intValue(), TagDomainEnums.ADMINISTRATIVE_REGION.getCode(), "定南县", option);
//            _generalParticipate(tenantId, south, MetricCodeIdEnum.SUPERMARKET_AREA_SOUTH, MetricCodeEnum.CUSTOMER_ADMIN_REGION, MetricCalcTypeEnum.METRIC_PARTICIPATE_IN, tagResponse);
//            tagResponse = treeTagReadService.queryTagLeafInfoByTagName(tenantId.intValue(), TagDomainEnums.ADMINISTRATIVE_REGION.getCode(), "安远县", option);
//            _generalParticipate(tenantId, south, MetricCodeIdEnum.SUPERMARKET_AREA_SOUTH, MetricCodeEnum.CUSTOMER_ADMIN_REGION, MetricCalcTypeEnum.METRIC_PARTICIPATE_IN, tagResponse);
            tagResponse = treeTagReadService.queryTagLeafInfoByTagName(tenantId.intValue(), TagDomainEnums.ADMINISTRATIVE_REGION.getCode(), "南部", option);
            _generalParticipate(tenantId, south, MetricCodeIdEnum.AREA_SOUTH, MetricCodeEnum.CUSTOMER_ADMIN_REGION, MetricCalcTypeEnum.METRIC_PARTICIPATE_IN, tagResponse);
        }


        // MetricCodeEnum.MARKET
        {
            Metric ganzhou = new Metric();
            ganzhou.setTenantId(tenantId);
            ganzhou.setMetricCode(MetricCodeIdEnum.MARKET_GANZHOU.getParentCode().getCode());
            ganzhou.setMetricCodeId(MetricCodeIdEnum.MARKET_GANZHOU.getCode());
            ganzhou.setMetricName(MetricCodeIdEnum.MARKET_GANZHOU.getName());
            ganzhou.setCalcType(0);
            ganzhou.setIsDeleted(0);
            ganzhou.setCreated(System.currentTimeMillis() / 1000);
            ganzhou.setCreatedBy(1L);
            ganzhou.setUpdated(System.currentTimeMillis() / 1000);
            ganzhou.setUpdatedBy(1L);
            metricRepository.save(ganzhou);
            BaseTreeTagQueryOption option = new BaseTreeTagQueryOption();
            option.setIncludeDisable(true);
            option.setQueryChild(true);
            MultiResponse<TagLeafInfoDto> tagResponse = treeTagReadService.queryTagLeafInfoByTagName(tenantId.intValue(), TagDomainEnums.SALES_REGION.getCode(), "广东区", option);
            _generalParticipate(tenantId, ganzhou, MetricCodeIdEnum.MARKET_GANZHOU, MetricCodeEnum.CUSTOMER_SALES_REGION, MetricCalcTypeEnum.METRIC_PARTICIPATE_NOT_IN, tagResponse);



            Metric guangdong = new Metric();
            guangdong.setTenantId(tenantId);
            guangdong.setMetricCode(MetricCodeIdEnum.MARKET_GUANGDONG.getParentCode().getCode());
            guangdong.setMetricCodeId(MetricCodeIdEnum.MARKET_GUANGDONG.getCode());
            guangdong.setMetricName(MetricCodeIdEnum.MARKET_GUANGDONG.getName());
            guangdong.setCalcType(0);
            guangdong.setIsDeleted(0);
            guangdong.setCreated(System.currentTimeMillis() / 1000);
            guangdong.setCreatedBy(1L);
            guangdong.setUpdated(System.currentTimeMillis() / 1000);
            guangdong.setUpdatedBy(1L);
            metricRepository.save(guangdong);
            tagResponse = treeTagReadService.queryTagLeafInfoByTagName(tenantId.intValue(), TagDomainEnums.SALES_REGION.getCode(), "广东区", option);
            _generalParticipate(tenantId, guangdong, MetricCodeIdEnum.MARKET_GUANGDONG, MetricCodeEnum.CUSTOMER_SALES_REGION, MetricCalcTypeEnum.METRIC_PARTICIPATE_IN, tagResponse);
        }

    }

    @Override
    public Map<Long, TagLeafInfoDto> queryTagInfo(Long tenantId, String metricCode, Set<Long> ids) {
        if (tenantId == null || CollectionUtils.isEmpty(ids) || metricCode == null) {
            return new HashMap<>();
        }
        if (MetricCodeEnum.CUSTOMER_TYPE.getCode().equals(metricCode)) {
            return queryTags(tenantId, TagDomainEnums.CUSTOMER_TYPE.getCode(), ids);
        }
        if (MetricCodeEnum.CUSTOMER_ADMIN_REGION.getCode().equals(metricCode)) {
            return queryTags(tenantId, TagDomainEnums.ADMINISTRATIVE_REGION.getCode(), ids);
        }
        if (MetricCodeEnum.CUSTOMER_SALES_REGION.getCode().equals(metricCode)) {
            return queryTags(tenantId, TagDomainEnums.SALES_REGION.getCode(), ids);
        }
        if (MetricCodeEnum.PRODUCT_TYPE.getCode().equals(metricCode)) {
            return queryTags(tenantId, TagDomainEnums.GOODS_CATEGORY.getCode(), ids);
        }
        return new HashMap<>();
    }


    @Override
    public Map<Long, String> queryNames(Long tenantId, String metricCode, Set<Long> ids) {
        Map<Long, String> result = Maps.newHashMap();
        if (tenantId == null || CollectionUtils.isEmpty(ids) || metricCode == null) {
            return result;
        }
        if (MetricCodeEnum.CUSTOMER_TYPE.getCode().equals(metricCode)) {
            queryTags(tenantId, TagDomainEnums.CUSTOMER_TYPE.getCode(), ids)
                    .forEach((id, tagLeafInfoDto) -> result.put(id, tagLeafInfoDto.getName()));
        }
        if (MetricCodeEnum.CUSTOMER_ADMIN_REGION.getCode().equals(metricCode)) {
            queryTags(tenantId, TagDomainEnums.ADMINISTRATIVE_REGION.getCode(), ids).
                    forEach((id, tagLeafInfoDto) -> result.put(id, tagLeafInfoDto.getName()));
        }
        if (MetricCodeEnum.CUSTOMER_SALES_REGION.getCode().equals(metricCode)) {
            queryTags(tenantId, TagDomainEnums.SALES_REGION.getCode(), ids)
                    .forEach((id, tagLeafInfoDto) -> result.put(id, tagLeafInfoDto.getName()));
        }
        if (MetricCodeEnum.CUSTOMER.getCode().equals(metricCode)) {
            queryCustomerInfo(tenantId, ids)
                    .forEach((id, customerInfoDto) -> result.put(id, customerInfoDto.getName()));
        }
        if (MetricCodeEnum.CUSTOMER_LEVEL.getCode().equals(metricCode)) {
            return CustomerLevelEnum.mappingName(ids);
        }
        if (MetricCodeEnum.CUSTOMER_OWNER.getCode().equals(metricCode)) {
            return queryUserInfo(tenantId, ids);
        }
        if (MetricCodeEnum.PRODUCT.getCode().equals(metricCode)) {
            queryGoodsInfo(tenantId, ids)
                    .forEach((id, goodsInfo) -> result.put(id, goodsInfo.getName()));
        }
        if (MetricCodeEnum.PRODUCT_TYPE.getCode().equals(metricCode)) {
            queryTags(tenantId, TagDomainEnums.GOODS_CATEGORY.getCode(), ids)
                    .forEach((id, tagLeafInfoDto) -> result.put(id, tagLeafInfoDto.getName()));
        }
        if (MetricCodeEnum.USER.getCode().equals(metricCode)) {
            return queryUserInfo(tenantId, ids);
        }
        if (MetricCodeEnum.DEPARTMENT.getCode().equals(metricCode)) {
            return queryDepartmentInfo(tenantId, ids);
        }
        return result;
    }


    private void _generalParticipate(
            long tenantId,
            Metric metric,
            MetricCodeIdEnum metricCodeIdEnum,
            MetricCodeEnum metricCodeEnum,
            MetricCalcTypeEnum calcTypeEnum,
            MultiResponse<TagLeafInfoDto> tagResponse) {
        for (TagLeafInfoDto tagInfo : tagResponse.getData()) {
            MetricParticipate metricParticipate = new MetricParticipate();
            metricParticipate.setTenantId(tenantId);
            metricParticipate.setMetricId(metric.getId());
            metricParticipate.setMetricCode(metricCodeIdEnum.getParentCode().getCode());
            metricParticipate.setMetricCodeId(metricCodeIdEnum.getCode());
            metricParticipate.setParticipateId(tagInfo.getId());
            metricParticipate.setParticipateMetricCode(metricCodeEnum.getCode());
            metricParticipate.setCalcType(calcTypeEnum.getCode());
            metricParticipate.setCreated(System.currentTimeMillis() / 1000);
            metricParticipate.setCreatedBy(1L);
            metricParticipate.setUpdated(System.currentTimeMillis() / 1000);
            metricParticipate.setUpdatedBy(1L);
            metricParticipate.setIsDeleted(0);
            metricParticipateRepository.save(metricParticipate);
        }
    }


}
