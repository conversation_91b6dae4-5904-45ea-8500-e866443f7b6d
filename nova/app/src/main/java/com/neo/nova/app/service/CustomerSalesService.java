package com.neo.nova.app.service;

import com.neo.api.PageResponse;
import com.neo.nova.app.vo.DataFormQueryVO;
import com.neo.nova.app.vo.DataFormVO;
import com.neo.nova.app.vo.PieChartVO;
import com.neo.nova.domain.dto.*;

import java.math.BigDecimal;
import java.util.Collection;
import java.util.List;
import java.util.Map;

public interface CustomerSalesService {
    DataFormVO statistics(DataFormQueryVO dataFormQueryVO);

    DataFormVO trends(DataFormQueryVO dataFormQueryVO);

    PieChartVO pieChart(Long tenantId, String currentTime);

    Map<Long, PerformanceProgressDTO> getProgress(Long tenantId, List<Long> userIds, TimeCondition timeCondition);

    List<Map<String, Object>> StatisticsExport(DataFormQueryVO dataFormQueryVO);


    List<Map<String, Object>> getDynamicExportData(DataFormQueryVO dataFormQueryVO);

    /**
     * 销售报表查询
     * 查询指定月份的销售数据，返回区域、负责人、门店、毛利额、订货额、实际销售额等信息
     *
     * @param queryVO 查询条件
     * @return 销售报表数据
     */
    DataFormVO salesReport(DataFormQueryVO queryVO);

    /**
     * 复杂指标数据查询
     * 不支持汇聚子树到父树
     *
     * @param dataFormQueryVO 默认分页查询
     * @return
     */
    PageResponse<Map<String, Object>> batchActualValue(DataFormQueryVO dataFormQueryVO);


    /**
     * 复杂趋势数据查询
     *
     * @param dataFormQueryVO 不分页查询
     * @return
     */
    PageResponse<Map<String, Object>> batchTrendValue(DataFormQueryVO dataFormQueryVO);

    /**
     * 图表数据查询
     * @param dataFormQueryVO 不分页查询
     * @return
     */
    Chart<String, Cell> queryChart(DataFormQueryVO dataFormQueryVO);

    /**
     * 获取真实数据
     * 支持汇聚子树到父树，例如查询部门会把所有子部门的数据汇总
     *
     * @param tenantId       租户id
     * @param participateIds 对象id
     * @param timeCondition  时间
     * @return
     */
    Map<String, BigDecimal> queryActualValue(Long tenantId, String metricCode, Collection<String> participateIds, TimeCondition timeCondition);

    /**
     * 获取真实数据
     *
     * @param tenantId      租户id
     * @param userIds       对象id
     * @param timeCondition 时间
     * @return
     */
    Map<Long, BigDecimal> queryActualValueForUser(Long tenantId, Collection<Long> userIds, TimeCondition timeCondition);

}
