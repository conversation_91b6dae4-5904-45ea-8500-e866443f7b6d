package com.neo.nova.app.service.impl;

import cn.hutool.db.PageResult;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Maps;
import com.neo.nova.app.action.actiontypes.OrderMessageAlarmType;
import com.neo.nova.app.action.enums.ActionRelationEnums;
import com.neo.nova.app.action.enums.WorkOrderPriorityEnums;
import com.neo.nova.app.exception.BizCustomException;
import com.neo.nova.app.exception.BizCustomException;
import com.neo.nova.app.service.EarlyWarningService;
import com.neo.nova.app.service.MetricService;
import com.neo.nova.app.vo.DataFormQueryVO;
import com.neo.nova.app.vo.DataFormVO;
import com.neo.nova.app.vo.WorkOrderQueryRequest;
import com.neo.nova.domain.dto.Cell;
import com.neo.nova.domain.dto.Chart;
import com.neo.nova.domain.dto.Column;
import com.neo.nova.domain.dto.Table;
import com.neo.nova.domain.dto.TimeCondition;
import com.neo.nova.domain.entity.CustomerInfo;
import com.neo.nova.domain.entity.WorkOrder;
import com.neo.nova.domain.entity.WorkOrderDetail;
import com.neo.nova.domain.enums.MetricCodeEnum;
import com.neo.nova.domain.enums.PeriodTypeEnum;
import com.neo.nova.domain.gateway.CustomerInfoRepository;
import com.neo.nova.domain.gateway.IWorkOrderDetailRepository;
import com.neo.nova.domain.gateway.IWorkOrderRepository;
import com.neo.session.SessionContextHolder;
import com.neo.tagcenter.client.rpc.TreeTagReadService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.YearMonth;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class EarlyWarningServiceImpl implements EarlyWarningService {

    @Autowired
    private IWorkOrderRepository workOrderRepository;

    @Autowired
    private CustomerInfoRepository customerInfoRepository;

    @Autowired
    private MetricService metricService;


    @Override
    public DataFormVO list(DataFormQueryVO dataFormQueryVO) {
        // 数据库分页查询获取统计数据和总数
        IPage pageResult = queryCustomerAlarmStatisticsWithPagination(dataFormQueryVO);

        // 批量获取客户信息并填充
        enrichCustomerInfo(pageResult.getRecords());

        // 构建返回结果
        DataFormVO result = new DataFormVO();
        result.setTable(buildTable(pageResult.getRecords(), pageResult.getTotal(), dataFormQueryVO));
        result.setChart(buildChart(pageResult.getRecords()));
        return result;
    }

    /**
     * 数据库分页查询获取客户预警统计
     */
    private IPage queryCustomerAlarmStatisticsWithPagination(DataFormQueryVO dataFormQueryVO) {
        // 构建查询条件
        QueryWrapper<WorkOrder> wrapper = buildQueryWrapper(dataFormQueryVO);

        // 添加分页条件
        IPage page = new Page<>(dataFormQueryVO.getPageIndex(), dataFormQueryVO.getPageSize());

        IPage<Map<String, Object>> data = workOrderRepository.getBaseMapper().selectMapsPage(page,wrapper);
        return data;
    }

    private QueryWrapper<WorkOrder> buildQueryWrapper(DataFormQueryVO dataFormQueryVO) {
        QueryWrapper<WorkOrder> queryWrapper = new QueryWrapper<>();

        queryWrapper.select("bizId as customerId,count(1) as alarmCount");

        // 排除已删除的记录
        queryWrapper.eq("tenantId", SessionContextHolder.getTenantId());
        queryWrapper.eq("deleted", 0);
        queryWrapper.eq("type", ActionRelationEnums.ORDER_MESSAGE_ALARM.getActionType());

        if(!CollectionUtils.isEmpty(dataFormQueryVO.getQueryOptions().get("status"))){
            queryWrapper.in("status", dataFormQueryVO.getQueryOptions().get("status"));
        }

        if(!CollectionUtils.isEmpty(dataFormQueryVO.getQueryOptions().get("type"))){
            queryWrapper.in("subType", dataFormQueryVO.getQueryOptions().get("type"));
        }

        //超时
        if(dataFormQueryVO.getIsTimeed() != null && dataFormQueryVO.getIsTimeed() == 1){
            queryWrapper.le("workOrderEndTime", LocalDateTime.now());
        } else if (dataFormQueryVO.getIsTimeed() != null && dataFormQueryVO.getIsTimeed() == 0) {
            queryWrapper.ge("workOrderEndTime", LocalDateTime.now());
        }

        //日期需要特别测试一下。
        if(dataFormQueryVO.getTimeCondition() != null){
           // queryWrapper.between("created", dataFormQueryVO.getTimeCondition().getStartDate(),dataFormQueryVO.getTimeCondition().getEndDate());
            if(dataFormQueryVO.getTimeCondition().getPeriodType() == PeriodTypeEnum.CUSTOM_DAY.getCode()){
                queryWrapper.ge("created",dataFormQueryVO.getTimeCondition().getStartDate());
                //增加一天
                DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
                // 字符串转LocalDate
                LocalDate date = LocalDate.parse(dataFormQueryVO.getTimeCondition().getEndDate(), formatter);
                // 增加一天
                LocalDate nextDay = date.plusDays(1);
                queryWrapper.lt("created",nextDay);
            }else if(dataFormQueryVO.getTimeCondition().getPeriodType() == PeriodTypeEnum.MONTH.getCode()){
                DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM");
                queryWrapper.ge("created",YearMonth.parse(dataFormQueryVO.getTimeCondition().getStartDate(),formatter).atDay(1));

                // 字符串转LocalDate
                LocalDate date = YearMonth.parse(dataFormQueryVO.getTimeCondition().getEndDate(), formatter).atDay(1);
                // 增加一月
                LocalDate nextMonth = date.plusMonths(1);
                queryWrapper.lt("created",nextMonth);
            }else {
                throw new BizCustomException(5008, "时间类型错误");
            }

        }
        queryWrapper.groupBy("bizId");
        queryWrapper.orderByDesc("alarmCount");

        if(dataFormQueryVO.getQueryOptions().containsKey("type")){
            queryWrapper.select( queryWrapper.getSqlSelect() +",subType");
            queryWrapper.groupBy("subType","bizId");
        }

        return queryWrapper;
    }


    /**
     * 查询客户预警统计总数
     */
//    private long queryCustomerAlarmStatisticsCount(DataFormQueryVO dataFormQueryVO) {
//        QueryWrapper<WorkOrderDetail> wrapper = new QueryWrapper<>();
//
//        // 基础查询条件
//        wrapper.eq("action", "orderMessageAlarm")
//               .eq("deleted", 0)
//               .apply("actionDetail LIKE '%\"customerId\":%'");
//
//        // 添加时间和类型条件
//        addTimeCondition(wrapper, dataFormQueryVO.getTimeCondition());
//        addTypeCondition(wrapper, dataFormQueryVO.getQueryOptions());
//
//        // 使用子查询统计不同客户ID的数量
//        wrapper.select("DISTINCT CAST(SUBSTRING_INDEX(SUBSTRING_INDEX(actionDetail, '\"customerId\":', -1), ',', 1) AS UNSIGNED) as customerId")
//               .having("customerId > 0");
//
//        List<Map<String, Object>> distinctCustomers = workOrderDetailRepository.getBaseMapper().selectMaps(wrapper);
//        return distinctCustomers.size();
//    }

    /**
     * 数据库分组查询获取客户预警统计（保留原方法用于备用）
     */
//    private List<Map<String, Object>> queryCustomerAlarmStatistics(DataFormQueryVO dataFormQueryVO) {
//        QueryWrapper<WorkOrderDetail> wrapper = new QueryWrapper<>();
//
//        // 基础查询条件
//        wrapper.eq("action", "orderMessageAlarm")
//               .eq("deleted", 0)
//               .apply("actionDetail LIKE '%\"customerId\":%'");
//
//        // 添加时间和类型条件
//        addTimeCondition(wrapper, dataFormQueryVO.getTimeCondition());
//        addTypeCondition(wrapper, dataFormQueryVO.getQueryOptions());
//
//        // 数据库分组统计：提取客户ID并按客户分组计数
//        wrapper.select(
//                "CAST(SUBSTRING_INDEX(SUBSTRING_INDEX(actionDetail, '\"customerId\":', -1), ',', 1) AS UNSIGNED) as customerId",
//                "COUNT(*) as alarmCount"
//        )
//        .groupBy("customerId")
//        .having("customerId > 0")
//        .orderByDesc("alarmCount");
//
//        return workOrderDetailRepository.getBaseMapper().selectMaps(wrapper);
//    }



    /**
     * 备用查询方法 - 查询原始数据然后在应用层处理
     */
//    private List<Map<String, Object>> fallbackQuery(DataFormQueryVO dataFormQueryVO) {
//        QueryWrapper<WorkOrderDetail> wrapper = new QueryWrapper<>();
//
//        // 基础查询条件
//        wrapper.eq("action", "orderMessageAlarm")
//               .eq("deleted", 0)
//               .isNotNull("actionDetail")
//               .ne("actionDetail", "");
//
//        // 添加时间条件
//        addTimeCondition(wrapper, dataFormQueryVO.getTimeCondition());
//
//        // 添加类型条件
//        addTypeCondition(wrapper, dataFormQueryVO.getQueryOptions());
//
//        if (dataFormQueryVO.getIsTimeed() == 1) {
//            LocalDateTime currentTime = LocalDateTime.now();
//            wrapper.lt("workOrderEndTime", currentTime);
//        }
//        // 查询原始数据
//        List<WorkOrderDetail> workOrderDetails = workOrderDetailRepository.list(wrapper);
//
//        // 在应用层进行分组统计
//        Map<Long, Integer> customerCounts = Maps.newHashMap();
//        for (WorkOrderDetail detail : workOrderDetails) {
//            Long customerId = parseCustomerIdSafely(detail.getActionDetail());
//            System.out.println("解析actionDetail: " + detail.getActionDetail() + " -> customerId: " + customerId);
//            if (customerId != null) {
//                customerCounts.put(customerId, customerCounts.getOrDefault(customerId, 0) + 1);
//            }
//        }
//
//
//        // 转换为返回格式
//        return customerCounts.entrySet().stream()
//                .sorted((a, b) -> Integer.compare(b.getValue(), a.getValue()))
//                .map(entry -> {
//                    Map<String, Object> result = Maps.newHashMap();
//                    result.put("customerId", entry.getKey().toString());
//                    result.put("alarmCount", entry.getValue().longValue());
//                    return result;
//                })
//                .collect(Collectors.toList());
//    }

    /**
     * 安全解析客户ID
     */
    private Long parseCustomerIdSafely(String actionDetail) {
        if (!StringUtils.hasText(actionDetail)) {
            return null;
        }

        try {
            // 尝试JSON解析
            if (actionDetail.trim().startsWith("{") && actionDetail.trim().endsWith("}")) {
                Map<String, Object> detailMap = JSON.parseObject(actionDetail, Map.class);
                Object customerIdObj = detailMap.get("customerId");
                if (customerIdObj != null) {
                    return Long.valueOf(customerIdObj.toString());
                }
            }
        } catch (Exception e) {
            // JSON解析失败，尝试字符串解析
            try {
                String pattern = "\"customerId\"\\s*:\\s*(\\d+)";
                java.util.regex.Pattern p = java.util.regex.Pattern.compile(pattern);
                java.util.regex.Matcher m = p.matcher(actionDetail);
                if (m.find()) {
                    return Long.valueOf(m.group(1));
                }
            } catch (Exception ex) {
                // 都失败则返回null
            }
        }

        return null;
    }

    /**
     * 添加时间条件
     */
    private void addTimeCondition(QueryWrapper<WorkOrderDetail> wrapper, TimeCondition timeCondition) {
        if (timeCondition == null) {
            return;
        }

        if (StringUtils.hasText(timeCondition.getStartDate())) {
            wrapper.ge("created", timeCondition.getStartDate() + " 00:00:00");
        }

        if (StringUtils.hasText(timeCondition.getEndDate())) {
            wrapper.le("created", timeCondition.getEndDate() + " 23:59:59");
        }
    }

    /**
     * 添加类型条件
     */
    private void addTypeCondition(QueryWrapper<WorkOrderDetail> wrapper, Map<String, List<String>> queryOptions) {
        if (queryOptions == null || queryOptions.isEmpty()) {
            return;
        }

        List<String> alarmTypes = queryOptions.get("alarmType");
        if (!CollectionUtils.isEmpty(alarmTypes)) {
            List<Integer> typeValues = alarmTypes.stream()
                    .map(Integer::valueOf)
                    .collect(Collectors.toList());
            wrapper.in("type", typeValues);
        }
        List<String> status = queryOptions.get("status");
        if (!CollectionUtils.isEmpty(status)) {
            List<Integer> statusValues = status.stream()
                    .map(Integer::valueOf)
                    .collect(Collectors.toList());
            wrapper.in("status", statusValues);
        }
    }

    /**
     * 批量获取客户信息并填充到统计数据中
     */
    private void enrichCustomerInfo(List<Map<String, Object>> statisticsData) {
        if (statisticsData.isEmpty()) {
            return;
        }

        // 提取所有客户ID
        List<Long> customerIds = statisticsData.stream()
                .map(data -> {
                    Object customerIdObj = data.get("customerId");
                    if (customerIdObj != null) {
                        try {
                            return Long.valueOf(customerIdObj.toString());
                        } catch (NumberFormatException e) {
                            return null;
                        }
                    }
                    return null;
                })
                .filter(id -> id != null)
                .collect(Collectors.toList());

        if (customerIds.isEmpty()) {
            return;
        }

        // 批量查询客户信息
        QueryWrapper<CustomerInfo> wrapper = new QueryWrapper<>();
        wrapper.in("id", customerIds)
               .eq("isDeleted", 0);

        List<CustomerInfo> customerInfos = customerInfoRepository.list(wrapper);

        // 创建客户信息映射
        Map<Long, CustomerInfo> customerInfoMap = customerInfos.stream()
                .collect(Collectors.toMap(CustomerInfo::getId, info -> info));

        //获取客户类型
        List<Long> customerTypeIds = customerInfos.stream()
                .map(CustomerInfo::getCustomerTypeId)
                .filter(id -> id != null)
                .distinct()
                .collect(Collectors.toList());
        Map<Long, String> tagMap = new HashMap<>();
        if(!CollectionUtils.isEmpty(customerTypeIds)){
            tagMap = metricService.queryNames(customerInfos.get(0).getTenantId(), MetricCodeEnum.CUSTOMER_TYPE.getCode(), new HashSet<>(customerTypeIds));
        }

        // 填充客户信息到统计数据
        for (Map<String, Object> data : statisticsData) {
            Object customerIdObj = data.get("customerId");
            if (customerIdObj != null) {
                try {
                    Long customerId = Long.valueOf(customerIdObj.toString());
                    CustomerInfo customerInfo = customerInfoMap.get(customerId);
                    if (customerInfo != null) {
                        data.put("customerName", customerInfo.getName());
                        data.put("customerTypeId", customerInfo.getCustomerTypeId());
                        data.put("customerTypeName", tagMap.get(customerInfo.getCustomerTypeId()));
                    } else {
                        data.put("customerName", "客户" + customerId);
                        data.put("customerTypeId", null);
                        data.put("customerTypeName", "未知");
                    }
                } catch (NumberFormatException e) {
                    data.put("customerName", "未知客户");
                    data.put("customerTypeId", null);
                    data.put("customerTypeName", "未知");
                }
            }
        }
    }

    /**
     * 获取客户类型名称
     */
    private String getCustomerTypeName(Long customerTypeId) {
        if (customerTypeId == null) {
            return "未知";
        }
        // 这里可以根据实际情况从客户类型表或缓存中获取类型名称
        return "类型" + customerTypeId;
    }

    /**
     * 构建表格数据
     */
    private Table buildTable(List<Map<String, Object>> statisticsData, long total, DataFormQueryVO queryVO) {
        Table table = new Table();
        table.setTitle("客户预警统计");

        // 构建表头
        buildTableColumns(table);

        if(queryVO.getQueryOptions().containsKey("type")){
            table.addToFirstColumn(Column.builder().key("type").title("预警类型").isSort(false).build());
        }

        // 构建表格数据
        buildTableData(table, statisticsData, total, queryVO);

        return table;
    }

    /**
     * 构建图表数据 - 柱状图
     */
    private Chart<String, String> buildChart(List<Map<String, Object>> statisticsData) {
        Chart<String, String> chart = new Chart<>();
        chart.setTitle("客户预警数量统计");

        if (statisticsData.isEmpty()) {
            return chart;
        }

        // 提取客户名称和预警数量
        List<String> customerNames = statisticsData.stream()
                .map(data -> getStringValue(data, "customerName", "未知客户"))
                .collect(java.util.stream.Collectors.toList());

        List<String> alarmCounts = statisticsData.stream()
                .map(data -> getIntValue(data, "alarmCount", 0).toString())
                .collect(java.util.stream.Collectors.toList());

        // 设置X轴（客户名称）
        chart.addXAxis("客户名称", customerNames);

        // 设置Y轴数据（预警数量）
        chart.addSeries("预警数量", alarmCounts);

        // 添加图表类型信息
        chart.addExtra("chartType", "bar");
        chart.addExtra("xAxisTitle", "客户名称");
        chart.addExtra("yAxisTitle", "预警数量");

        return chart;
    }

    /**
     * 构建表头
     */
    private void buildTableColumns(Table table) {
        String[] columns = {"index", "customerName", "customerType", "alarmCount"};
        String[] titles = {"序号", "客户名称", "客户类型", "预警数量"};
        boolean[] sortable = {false, false, false, true};

        for (int i = 0; i < columns.length; i++) {
            table.addToFirstColumn(Column.builder()
                    .key(columns[i])
                    .title(titles[i])
                    .columnType(0)
                    .isSort(sortable[i])
                    .isFixed(false)
                    .build());
        }
    }

    /**
     * 构建表格数据
     */
    private void buildTableData(Table table, List<Map<String, Object>> statisticsData, long total, DataFormQueryVO queryVO) {
        // 计算起始序号（考虑分页）
        int startIndex = queryVO.getOffset() + 1;
        int index = startIndex;

        for (Map<String, Object> data : statisticsData) {
            Map<String, Cell> rowData = Maps.newHashMap();

            rowData.put("index", Cell.builder().value(index++).build());
            rowData.put("customerName", Cell.builder().value(getStringValue(data, "customerName", "未知客户")).build());
            rowData.put("customerType", Cell.builder().value(getStringValue(data, "customerTypeName", "未知类型")).build());
            rowData.put("alarmCount", Cell.builder().value(getIntValue(data, "alarmCount", 0)).build());
            if(queryVO.getQueryOptions().containsKey("type")){
                rowData.put("type", Cell.builder().value(OrderMessageAlarmType.map.get(getIntValue(data, "subType", 0)).getName()).build());
            }

            table.addRow(rowData);
        }

        // 设置真实的分页信息
        table.setTotal(total);
        table.setCurrentPage((long) queryVO.getPageIndex());
        table.setPageSize((long) queryVO.getPageSize());

        // 计算总页数
        long totalPages = (total + queryVO.getPageSize() - 1) / queryVO.getPageSize();
        table.setTotalPage(totalPages);
    }

    /**
     * 安全获取字符串值
     */
    private String getStringValue(Map<String, Object> data, String key, String defaultValue) {
        Object value = data.get(key);
        return value != null ? value.toString() : defaultValue;
    }

    /**
     * 安全获取整数值
     */
    private Integer getIntValue(Map<String, Object> data, String key, Integer defaultValue) {
        Object value = data.get(key);
        if (value == null) return defaultValue;

        try {
            return value instanceof Number ? ((Number) value).intValue() : Integer.valueOf(value.toString());
        } catch (NumberFormatException e) {
            return defaultValue;
        }
    }


}
