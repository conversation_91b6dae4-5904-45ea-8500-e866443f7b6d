package com.neo.nova.app.helper;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.excel.write.metadata.WriteTable;
import com.alibaba.excel.write.metadata.style.WriteCellStyle;
import com.alibaba.excel.write.metadata.style.WriteFont;
import com.alibaba.excel.write.style.HorizontalCellStyleStrategy;
import com.neo.nova.domain.enums.MetricCodeEnum;
import com.neo.nova.domain.excelExport.DynamicStatisticsExport;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.poi.ss.usermodel.IndexedColors;

import java.io.OutputStream;
import java.util.*;

/**
 * 动态Excel导出工具类
 */
public class DynamicExcelHelper {

    /**
     * 写入动态Excel
     *
     * @param outputStream 输出流
     * @param data 数据列表
     * @param columnMapping 列映射（字段名 -> 列标题）
     * @param sheetName 工作表名称
     */
    public static void writeDynamicExcel(OutputStream outputStream,
                                       List<Map<String, Object>> data,
                                       Map<String, String> columnMapping,
                                       String sheetName) {

        // 构建表头
        List<List<String>> headers = buildHeaders(columnMapping);

        // 构建数据行
        List<List<Object>> rows = buildDataRows(data, columnMapping);

        // 创建样式策略
        HorizontalCellStyleStrategy styleStrategy = createStyleStrategy();

        // 写入Excel
        EasyExcel.write(outputStream)
                .registerWriteHandler(styleStrategy)
                .head(headers)
                .sheet(sheetName)
                .doWrite(rows);
    }
    public static void writeStatisticsExcel(OutputStream outputStream,
                                         List<Map<String, Object>> data,
                                         Map<String, String> columnMapping,
                                         String sheetName) {

        // 构建表头
        List<List<String>> headers = buildStatisticsHeaders(columnMapping);

        // 构建数据行
        List<List<Object>> rows = buildStatisticsDataRows(data, columnMapping);

        // 创建样式策略
        HorizontalCellStyleStrategy styleStrategy = createStyleStrategy();

        // 写入Excel
        EasyExcel.write(outputStream)
                .registerWriteHandler(styleStrategy)
                .head(headers)
                .sheet(sheetName)
                .doWrite(rows);
    }

    /**
     * 构建表头
     */
    private static List<List<String>> buildHeaders(Map<String, String> columnMapping) {
        List<List<String>> headers = new ArrayList<>();

        // 固定列：序号、统计日期、销售额
        headers.add(Arrays.asList("序号"));
        headers.add(Arrays.asList("统计日期"));

        // 动态列：根据查询条件添加
        for (Map.Entry<String, String> entry : columnMapping.entrySet()) {
            headers.add(Arrays.asList(entry.getValue()));
        }



        return headers;
    }
    /**
     * 构建表头
     */
    private static List<List<String>> buildStatisticsHeaders(Map<String, String> columnMapping) {
        List<List<String>> headers = new ArrayList<>();

        // 固定列：序号、统计日期、销售额
        headers.add(Arrays.asList("序号"));

        // 动态列：根据查询条件添加
        for (Map.Entry<String, String> entry : columnMapping.entrySet()) {
            headers.add(Arrays.asList(entry.getValue()));
        }



        return headers;
    }
    /**
     * 构建数据行
     */
    private static List<List<Object>> buildStatisticsDataRows(List<Map<String, Object>> data,
                                                    Map<String, String> columnMapping) {
        List<List<Object>> rows = new ArrayList<>();

        for (int i = 0; i < data.size(); i++) {
            Map<String, Object> record = data.get(i);
            List<Object> row = new ArrayList<>();

            // 序号
            row.add(i + 1);


            // 动态列数据
            for (String columnName : columnMapping.keySet()) {
                if ("salesAmountRatio".equals(columnName) || "salesAmountMomGrowth".equals(columnName) || "salesAmountYoyGrowth".equals(columnName)) {
                    // 保留两位小数
                    Object value = record.get(columnName);
                    if (value != null) {
                        try {
                            double doubleValue = Double.parseDouble(value.toString());
                            row.add(String.format("%.2f", doubleValue));
                        } catch (NumberFormatException e) {
                            row.add(getStringValue(record, columnName));
                        }
                    } else {
                        row.add("");
                    }
                } else {
                    row.add(getStringValue(record, columnName));
                }
            }

            rows.add(row);
        }

        return rows;
    }

    /**
     * 构建数据行
     */
    private static List<List<Object>> buildDataRows(List<Map<String, Object>> data,
                                                   Map<String, String> columnMapping) {
        List<List<Object>> rows = new ArrayList<>();

        for (int i = 0; i < data.size(); i++) {
            Map<String, Object> record = data.get(i);
            List<Object> row = new ArrayList<>();

            // 序号
            row.add(i + 1);

            // 统计日期
            row.add(getStringValue(record, "visit_date"));


            // 动态列数据
            for (String columnName : columnMapping.keySet()) {
                if ( "salesAmountMomGrowth".equals(columnName) || "salesAmountYoyGrowth".equals(columnName)) {
                    // 保留两位小数
                    Object value = record.get(columnName);
                    if (value != null) {
                        try {
                            double doubleValue = Double.parseDouble(value.toString());
                            row.add(String.format("%.2f", doubleValue));
                        } catch (NumberFormatException e) {
                            row.add(getStringValue(record, columnName));
                        }
                    } else {
                        row.add("");
                    }
                } else {
                    row.add(getStringValue(record, columnName));
                }
            }

            rows.add(row);
        }

        return rows;
    }

    /**
     * 创建样式策略
     */
    private static HorizontalCellStyleStrategy createStyleStrategy() {
        // 表头样式
        WriteCellStyle headWriteCellStyle = new WriteCellStyle();
        headWriteCellStyle.setFillForegroundColor(IndexedColors.GREY_25_PERCENT.getIndex());
        WriteFont headWriteFont = new WriteFont();
        headWriteFont.setFontHeightInPoints((short) 12);
        headWriteFont.setBold(true);
        headWriteCellStyle.setWriteFont(headWriteFont);
        headWriteCellStyle.setHorizontalAlignment(HorizontalAlignment.CENTER);

        // 内容样式
        WriteCellStyle contentWriteCellStyle = new WriteCellStyle();
        contentWriteCellStyle.setHorizontalAlignment(HorizontalAlignment.CENTER);

        return new HorizontalCellStyleStrategy(headWriteCellStyle, contentWriteCellStyle);
    }

    /**
     * 安全获取String值
     */
    private static String getStringValue(Map<String, Object> map, String key) {
        Object value = map.get(key);
        return value != null ? value.toString() : "";
    }

    /**
     * 安全获取Long值
     */
    private static Long getLongValue(Map<String, Object> map, String key) {
        Object value = map.get(key);
        if (value == null) return 0L;
        if (value instanceof Long) return (Long) value;
        if (value instanceof Integer) return ((Integer) value).longValue();
        try {
            return Long.valueOf(value.toString());
        } catch (NumberFormatException e) {
            return 0L;
        }
    }

    /**
     * 根据查询条件构建列映射
     */
    public static Map<String, String> buildColumnMapping(Map<String, List<String>> queryOptions) {
        Map<String, String> columnMapping = new LinkedHashMap<>();


        if (queryOptions != null) {
            queryOptions.forEach((metricCode, values) -> {
                String columnName = MetricCodeEnum.metricCodeMapping(metricCode);
                String columnTitle = getColumnTitle(metricCode);
                if (columnName != null && columnTitle != null) {
                    columnMapping.put(getColumnName(columnName), columnTitle);
                }
            });
        }
        columnMapping.put("salesAmount","销售额");
        columnMapping.put("salesAmountRatio","销售额占比");
        columnMapping.put("salesAmountMomGrowth","销售额环比增长");
        columnMapping.put("salesAmountYoyGrowth","销售额同比增长");

        return columnMapping;
    }
    /**
     * 根据查询条件构建列映射
     */
    public static Map<String, String> buildTrendColumnMapping(Map<String, List<String>> queryOptions) {
        Map<String, String> columnMapping = new LinkedHashMap<>();


        if (queryOptions != null) {
            queryOptions.forEach((metricCode, values) -> {
                String columnName = MetricCodeEnum.metricCodeMapping(metricCode);
                String columnTitle = getColumnTitle(metricCode);
                if (columnName != null && columnTitle != null) {
                    columnMapping.put(getColumnName(columnName), columnTitle);
                }
            });
        }
        columnMapping.put("salesAmount","销售额");
        columnMapping.put("salesAmountMomGrowth","销售额环比增长");
        columnMapping.put("salesAmountYoyGrowth","销售额同比增长");

        return columnMapping;
    }

    public static String getColumnName(String metricCode) {
        return switch (metricCode) {
            case "channelId" -> "channelName";
            case "produceTypeId" -> "produceTypeName";
            case "supermarketAreaId" -> "supermarketAreaName";
            case "marketId" -> "marketName";
            case "adminRegionId" -> "adminRegionName";
            case "salesRegionId" -> "salesRegionName";
            default -> " ";
        };
     }


    /**
     * 获取列标题
     */
    private static String getColumnTitle(String metricCode) {
        return switch (metricCode) {
            case "C_CHANNEL" -> "渠道";
            case "C_AREA" -> "大区";
            case "D_CUSTOMER_ADMIN_REGION" -> "行政区域";
            case "D_CUSTOMER_LEVEL" -> "客户等级";
            case "D_CUSTOMER_OWNER" -> "业务员";
            case "D_CUSTOMER_NAME" -> "客户名称";
            case "D_PRODUCT_TYPE" -> "产品类型";
            case "D_PRODUCT_NAME" -> "产品名称";
            case "C_MARKET" ->"市场";
            case "D_CUSTOMER_SALES_REGION"->"销售区域";
            default -> null;
        };
    }
}
