package com.neo.nova.app.vo;

import com.neo.nova.domain.dto.TimeCondition;
import lombok.Data;


/**
 * 销售报表查询请求VO
 *
 * <AUTHOR>
 * @since 2025-07-24
 */
@Data
public class SalesReportQueryVO {

    /**
     * 必填、目前仅支持月份
     */
    private TimeCondition timeCondition;

    /**
     * 必填、渠道id
     */
    private String channelMetricCodeId;

    /**
     * 前端不传 租户ID
     */
    private Long tenantId;

    /**
     * 前端不传 用户ID
     */
    private Long userId;

}
